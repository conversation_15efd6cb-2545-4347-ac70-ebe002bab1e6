import { useState, useEffect, useCallback, useRef } from 'react';
import { dataService } from '@/lib/supabase/dataService';
import { supabase } from '@/lib/supabase';
import type { 
  Transformation, 
  AgentLog, 
  NotificationData, 
  SystemHealth,
  RealtimePayload 
} from '@/lib/supabase/types';

interface UseRealtimeDataOptions {
  enabled?: boolean;
  userId?: string;
  transformationId?: string;
}

interface RealtimeDataState {
  transformations: Transformation[];
  agentLogs: AgentLog[];
  notifications: NotificationData[];
  systemHealth: SystemHealth | null;
  isLoading: boolean;
  error: string | null;
  connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error';
}

export function useRealtimeData(options: UseRealtimeDataOptions = {}) {
  const { enabled = true, userId, transformationId } = options;
  
  const [state, setState] = useState<RealtimeDataState>({
    transformations: [],
    agentLogs: [],
    notifications: [],
    systemHealth: null,
    isLoading: true,
    error: null,
    connectionStatus: 'disconnected'
  });

  const subscriptionsRef = useRef<any[]>([]);
  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const retryCountRef = useRef(0);
  const maxRetries = 5;

  // Update state helper
  const updateState = useCallback((updates: Partial<RealtimeDataState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Error handler
  const handleError = useCallback((error: any, context: string) => {
    console.error(`Realtime data error (${context}):`, error);
    updateState({ 
      error: `${context}: ${error.message || 'Unknown error'}`,
      connectionStatus: 'error'
    });
  }, [updateState]);

  // Load initial data
  const loadInitialData = useCallback(async () => {
    if (!enabled || !userId) return;

    try {
      updateState({ isLoading: true, error: null });

      const [transformations, notifications, systemHealthHistory] = await Promise.all([
        dataService.getUserTransformations(userId, 50),
        dataService.getUserNotifications(userId, 50),
        dataService.getSystemHealthHistory(1) // Last hour
      ]);

      const agentLogs = transformationId 
        ? await dataService.getTransformationLogs(transformationId)
        : [];

      updateState({
        transformations,
        agentLogs,
        notifications,
        systemHealth: systemHealthHistory[0] || null,
        isLoading: false,
        connectionStatus: 'connected'
      });

      retryCountRef.current = 0;
    } catch (error) {
      handleError(error, 'Initial data load');
      updateState({ isLoading: false });
    }
  }, [enabled, userId, transformationId, updateState, handleError]);

  // Setup real-time subscriptions
  const setupSubscriptions = useCallback(() => {
    if (!enabled || !userId) return;

    try {
      updateState({ connectionStatus: 'connecting' });

      // Clear existing subscriptions
      subscriptionsRef.current.forEach(sub => {
        if (sub && typeof sub.unsubscribe === 'function') {
          sub.unsubscribe();
        }
      });
      subscriptionsRef.current = [];

      // Subscribe to transformations
      const transformationsChannel = dataService.subscribeToTransformations(
        userId,
        (payload: RealtimePayload<Transformation>) => {
          const { eventType, new: newRecord, old: oldRecord } = payload;
          
          setState(prev => {
            let updatedTransformations = [...prev.transformations];
            
            switch (eventType) {
              case 'INSERT':
                updatedTransformations = [newRecord, ...updatedTransformations];
                break;
              case 'UPDATE':
                updatedTransformations = updatedTransformations.map(t => 
                  t.id === newRecord.id ? newRecord : t
                );
                break;
              case 'DELETE':
                updatedTransformations = updatedTransformations.filter(t => 
                  t.id !== oldRecord.id
                );
                break;
            }
            
            return { ...prev, transformations: updatedTransformations };
          });
        }
      );

      // Subscribe to notifications
      const notificationsChannel = dataService.subscribeToNotifications(
        userId,
        (payload: RealtimePayload<NotificationData>) => {
          const { eventType, new: newRecord, old: oldRecord } = payload;
          
          setState(prev => {
            let updatedNotifications = [...prev.notifications];
            
            switch (eventType) {
              case 'INSERT':
                updatedNotifications = [newRecord, ...updatedNotifications];
                break;
              case 'UPDATE':
                updatedNotifications = updatedNotifications.map(n => 
                  n.id === newRecord.id ? newRecord : n
                );
                break;
              case 'DELETE':
                updatedNotifications = updatedNotifications.filter(n => 
                  n.id !== oldRecord.id
                );
                break;
            }
            
            return { ...prev, notifications: updatedNotifications };
          });
        }
      );

      // Subscribe to agent logs if transformation ID is provided
      let agentLogsChannel;
      if (transformationId) {
        agentLogsChannel = dataService.subscribeToAgentLogs(
          transformationId,
          (payload: RealtimePayload<AgentLog>) => {
            const { eventType, new: newRecord, old: oldRecord } = payload;
            
            setState(prev => {
              let updatedLogs = [...prev.agentLogs];
              
              switch (eventType) {
                case 'INSERT':
                  updatedLogs = [...updatedLogs, newRecord].sort((a, b) => 
                    a.iteration - b.iteration
                  );
                  break;
                case 'UPDATE':
                  updatedLogs = updatedLogs.map(log => 
                    log.id === newRecord.id ? newRecord : log
                  );
                  break;
                case 'DELETE':
                  updatedLogs = updatedLogs.filter(log => 
                    log.id !== oldRecord.id
                  );
                  break;
              }
              
              return { ...prev, agentLogs: updatedLogs };
            });
          }
        );
      }

      // Subscribe to system health
      const systemHealthChannel = dataService.subscribeToSystemHealth(
        (payload: RealtimePayload<SystemHealth>) => {
          if (payload.eventType === 'INSERT') {
            updateState({ systemHealth: payload.new });
          }
        }
      );

      // Store subscriptions for cleanup
      subscriptionsRef.current = [
        transformationsChannel,
        notificationsChannel,
        agentLogsChannel,
        systemHealthChannel
      ].filter(Boolean);

      updateState({ connectionStatus: 'connected', error: null });
      retryCountRef.current = 0;

    } catch (error) {
      handleError(error, 'Subscription setup');
      scheduleRetry();
    }
  }, [enabled, userId, transformationId, updateState, handleError]);

  // Retry connection with exponential backoff
  const scheduleRetry = useCallback(() => {
    if (retryCountRef.current >= maxRetries) {
      updateState({ 
        connectionStatus: 'error',
        error: 'Max retry attempts reached. Please refresh the page.'
      });
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, retryCountRef.current), 30000);
    retryCountRef.current++;

    updateState({ connectionStatus: 'connecting' });

    retryTimeoutRef.current = setTimeout(() => {
      setupSubscriptions();
    }, delay);
  }, [setupSubscriptions, updateState]);

  // Manual refresh
  const refresh = useCallback(async () => {
    await loadInitialData();
    setupSubscriptions();
  }, [loadInitialData, setupSubscriptions]);

  // Mark notification as read
  const markNotificationRead = useCallback(async (notificationId: string) => {
    try {
      await dataService.markNotificationRead(notificationId);
      // The real-time subscription will update the state automatically
    } catch (error) {
      handleError(error, 'Mark notification read');
    }
  }, [handleError]);

  // Get transformation by ID
  const getTransformation = useCallback((id: string) => {
    return state.transformations.find(t => t.id === id);
  }, [state.transformations]);

  // Get unread notifications count
  const unreadNotificationsCount = state.notifications.filter(n => !n.read).length;

  // Setup effect
  useEffect(() => {
    if (enabled && userId) {
      loadInitialData();
      setupSubscriptions();
    }

    return () => {
      // Cleanup subscriptions
      subscriptionsRef.current.forEach(sub => {
        if (sub && typeof sub.unsubscribe === 'function') {
          sub.unsubscribe();
        }
      });
      subscriptionsRef.current = [];

      // Clear retry timeout
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [enabled, userId, transformationId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      subscriptionsRef.current.forEach(sub => {
        if (sub && typeof sub.unsubscribe === 'function') {
          sub.unsubscribe();
        }
      });
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...state,
    unreadNotificationsCount,
    refresh,
    markNotificationRead,
    getTransformation,
    retry: setupSubscriptions
  };
}

// Hook for specific transformation real-time updates
export function useTransformationRealtime(transformationId: string | null) {
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const user = await dataService.getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('Failed to get current user:', error);
      }
    };

    getCurrentUser();
  }, []);

  return useRealtimeData({
    enabled: !!transformationId && !!currentUser,
    userId: currentUser?.id,
    transformationId: transformationId || undefined
  });
}

// Hook for dashboard overview data
export function useDashboardRealtime() {
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const user = await dataService.getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('Failed to get current user:', error);
      }
    };

    getCurrentUser();
  }, []);

  return useRealtimeData({
    enabled: !!currentUser,
    userId: currentUser?.id
  });
}
