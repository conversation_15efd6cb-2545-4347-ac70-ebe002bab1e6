import { supabase, isSupabaseConfigured } from './supabase';

export interface TelemetryEvent {
  event_type: string;
  event_data?: Record<string, any>;
  session_id?: string;
  user_agent?: string;
  ip_address?: string;
}

export interface ReactorRunEvent {
  prompt: string;
  max_iterations: number;
  score_threshold: number;
  final_score?: number;
  iterations_count: number;
  execution_time_ms: number;
  cost_usd: number;
  status: 'completed' | 'failed' | 'timeout';
  model_planner: string;
  model_critic: string;
}

export interface PRCreatedEvent {
  repo_owner: string;
  repo_name: string;
  pr_number: number;
  confidence_score: number;
  operations_count: number;
}

class TelemetryService {
  private sessionId: string;
  private _isEnabled: boolean = true;
  private _isInitialized: boolean = false;
  private _initializationPromise: Promise<void>;

  constructor() {
    this.sessionId = this.generateSessionId();
    this._initializationPromise = this.loadTelemetrySettings();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private async loadTelemetrySettings(): Promise<void> {
    try {
      // Check if Supabase is properly configured
      if (!isSupabaseConfigured()) {
        console.warn('Supabase not configured, telemetry disabled');
        this._isEnabled = false;
        this._isInitialized = true;
        return;
      }

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError) {
        // Authentication not available, using default telemetry settings for anonymous users
        this._isEnabled = true; // Default to enabled for anonymous users
        this._isInitialized = true;
        return;
      }

      if (!user) {
        // Default to enabled for anonymous users
        this._isEnabled = true;
        this._isInitialized = true;
        return;
      }

      const { data, error } = await supabase
        .from('settings')
        .select('telemetry_enabled')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.warn('Failed to load telemetry settings:', error);
        this._isEnabled = true; // Default to enabled on error
        this._isInitialized = true;
        return;
      }

      this._isEnabled = data?.telemetry_enabled ?? true;
      this._isInitialized = true;
    } catch (error) {
      console.warn('Error loading telemetry settings:', error);
      this._isEnabled = true; // Default to enabled on error
      this._isInitialized = true;
    }
  }

  async setTelemetryEnabled(enabled: boolean): Promise<void> {
    this._isEnabled = enabled;

    try {
      // Check if Supabase is properly configured
      if (!isSupabaseConfigured()) {
        console.warn('Supabase not configured, cannot save telemetry settings');
        return;
      }

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError) {
        console.warn('Authentication error, cannot save telemetry settings:', authError);
        return;
      }

      if (!user) return;

      const { error } = await supabase
        .from('settings')
        .upsert({
          user_id: user.id,
          telemetry_enabled: enabled,
        }, { onConflict: 'user_id' });

      if (error) {
        console.error('Failed to update telemetry settings:', error);
      }
    } catch (error) {
      console.error('Error updating telemetry settings:', error);
    }
  }

  private async trackEvent(event: TelemetryEvent): Promise<void> {
    if (!this._isEnabled) return;

    try {
      // Check if Supabase is properly configured
      if (!isSupabaseConfigured()) {
        // Silently skip tracking if Supabase is not configured
        return;
      }

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError) {
        // Silently skip tracking on auth errors
        return;
      }

      const telemetryData = {
        ...event,
        user_id: user?.id || null, // Allow anonymous tracking
        session_id: this.sessionId,
        user_agent: navigator.userAgent,
        created_at: new Date().toISOString(),
      };

      // Remove IP address for privacy (let server handle if needed)
      delete telemetryData.ip_address;

      const { error } = await supabase
        .from('telemetry_events')
        .insert(telemetryData);

      if (error) {
        console.warn('Failed to track telemetry event:', error);
      }
    } catch (error) {
      console.warn('Error tracking telemetry event:', error);
    }
  }

  // Application lifecycle events
  async trackAppStart(): Promise<void> {
    await this.trackEvent({
      event_type: 'app_start',
      event_data: {
        timestamp: Date.now(),
        url: window.location.href,
        referrer: document.referrer,
      },
    });
  }

  async trackPageView(page: string): Promise<void> {
    await this.trackEvent({
      event_type: 'page_view',
      event_data: {
        page,
        url: window.location.href,
        timestamp: Date.now(),
      },
    });
  }

  // Reactor loop events
  async trackReactorRunStart(data: Partial<ReactorRunEvent>): Promise<void> {
    await this.trackEvent({
      event_type: 'reactor_run_start',
      event_data: {
        prompt_length: data.prompt?.length || 0,
        max_iterations: data.max_iterations,
        score_threshold: data.score_threshold,
        model_planner: data.model_planner,
        model_critic: data.model_critic,
        timestamp: Date.now(),
      },
    });
  }

  async trackReactorRunEnd(data: ReactorRunEvent): Promise<void> {
    await this.trackEvent({
      event_type: 'reactor_run_end',
      event_data: {
        ...data,
        prompt_length: data.prompt.length,
        prompt: undefined, // Don't store actual prompt for privacy
        timestamp: Date.now(),
      },
    });
  }

  async trackReactorIteration(iteration: number, score: number, planTokens: number, critiqueTokens: number): Promise<void> {
    await this.trackEvent({
      event_type: 'reactor_iteration',
      event_data: {
        iteration,
        score,
        plan_tokens: planTokens,
        critique_tokens: critiqueTokens,
        timestamp: Date.now(),
      },
    });
  }

  // GitHub integration events
  async trackGitHubConnect(username: string): Promise<void> {
    await this.trackEvent({
      event_type: 'github_connect',
      event_data: {
        username,
        timestamp: Date.now(),
      },
    });
  }

  async trackGitHubDisconnect(): Promise<void> {
    await this.trackEvent({
      event_type: 'github_disconnect',
      event_data: {
        timestamp: Date.now(),
      },
    });
  }

  async trackPRCreated(data: PRCreatedEvent): Promise<void> {
    await this.trackEvent({
      event_type: 'pr_created',
      event_data: {
        ...data,
        timestamp: Date.now(),
      },
    });
  }

  // Settings events
  async trackSettingsChanged(changes: Record<string, any>): Promise<void> {
    await this.trackEvent({
      event_type: 'settings_changed',
      event_data: {
        changes,
        timestamp: Date.now(),
      },
    });
  }

  // Error tracking
  async trackError(error: Error, context?: string): Promise<void> {
    await this.trackEvent({
      event_type: 'error',
      event_data: {
        error_message: error.message,
        error_stack: error.stack,
        context,
        timestamp: Date.now(),
      },
    });
  }

  // Feature usage tracking
  async trackFeatureUsed(feature: string, data?: Record<string, any>): Promise<void> {
    await this.trackEvent({
      event_type: 'feature_used',
      event_data: {
        feature,
        ...data,
        timestamp: Date.now(),
      },
    });
  }

  // Performance tracking
  async trackPerformance(metric: string, value: number, unit: string = 'ms'): Promise<void> {
    await this.trackEvent({
      event_type: 'performance',
      event_data: {
        metric,
        value,
        unit,
        timestamp: Date.now(),
      },
    });
  }

  // User engagement
  async trackUserAction(action: string, data?: Record<string, any>): Promise<void> {
    await this.trackEvent({
      event_type: 'user_action',
      event_data: {
        action,
        ...data,
        timestamp: Date.now(),
      },
    });
  }

  // Batch tracking for efficiency
  private eventQueue: TelemetryEvent[] = [];
  private flushTimeout: NodeJS.Timeout | null = null;

  private async flushEvents(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    const events = [...this.eventQueue];
    this.eventQueue = [];

    try {
      // Check if Supabase is properly configured
      if (!isSupabaseConfigured()) {
        // Silently skip flushing if Supabase is not configured
        return;
      }

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError) {
        // Silently skip flushing on auth errors
        return;
      }

      const telemetryData = events.map(event => ({
        ...event,
        user_id: user?.id || null,
        session_id: this.sessionId,
        user_agent: navigator.userAgent,
        created_at: new Date().toISOString(),
      }));

      const { error } = await supabase
        .from('telemetry_events')
        .insert(telemetryData);

      if (error) {
        console.warn('Failed to flush telemetry events:', error);
      }
    } catch (error) {
      console.warn('Error flushing telemetry events:', error);
    }
  }

  async trackEventBatched(event: TelemetryEvent): Promise<void> {
    if (!this._isEnabled) return;

    this.eventQueue.push(event);

    // Flush events every 5 seconds or when queue reaches 10 events
    if (this.eventQueue.length >= 10) {
      await this.flushEvents();
    } else if (!this.flushTimeout) {
      this.flushTimeout = setTimeout(async () => {
        await this.flushEvents();
        this.flushTimeout = null;
      }, 5000);
    }
  }

  // Cleanup
  async flush(): Promise<void> {
    if (this.flushTimeout) {
      clearTimeout(this.flushTimeout);
      this.flushTimeout = null;
    }
    await this.flushEvents();
  }

  // Privacy controls
  isEnabled(): boolean {
    return this._isEnabled;
  }

  // Check if the service has finished initializing
  isInitialized(): boolean {
    return this._isInitialized;
  }

  // Wait for initialization to complete
  async waitForInitialization(): Promise<void> {
    await this._initializationPromise;
  }

  getSessionId(): string {
    return this.sessionId;
  }
}

// Global telemetry instance
export const telemetry = new TelemetryService();

// Auto-track app start
telemetry.trackAppStart();

// Flush events before page unload
window.addEventListener('beforeunload', () => {
  telemetry.flush();
});
