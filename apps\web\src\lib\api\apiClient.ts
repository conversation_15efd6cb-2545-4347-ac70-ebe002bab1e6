import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorSeverity } from '@/lib/errors/errorHandler';

// API Configuration
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  headers?: Record<string, string>;
}

// Request/Response types
export interface ApiRequest {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
  skipAuth?: boolean;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: ApiRequest;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  data?: any;
  config?: ApiRequest;
}

// Interceptor types
export type RequestInterceptor = (config: ApiRequest) => ApiRequest | Promise<ApiRequest>;
export type ResponseInterceptor = (response: ApiR<PERSON>ponse) => ApiResponse | Promise<ApiResponse>;
export type ErrorInterceptor = (error: ApiError) => Promise<never>;

// Retry configuration
export interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: ApiError) => boolean;
  exponentialBackoff?: boolean;
  maxRetryDelay?: number;
}

export class ApiClient {
  private config: ApiConfig;
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];
  private errorInterceptors: ErrorInterceptor[] = [];

  constructor(config: Partial<ApiConfig> = {}) {
    this.config = {
      baseURL: '',
      timeout: 30000,
      retries: 3,
      retryDelay: 1000,
      ...config
    };

    // Add default interceptors
    this.addDefaultInterceptors();
  }

  // Request methods
  async get<T = any>(url: string, config?: Partial<ApiRequest>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'GET' });
  }

  async post<T = any>(url: string, data?: any, config?: Partial<ApiRequest>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'POST', data });
  }

  async put<T = any>(url: string, data?: any, config?: Partial<ApiRequest>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'PUT', data });
  }

  async patch<T = any>(url: string, data?: any, config?: Partial<ApiRequest>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'PATCH', data });
  }

  async delete<T = any>(url: string, config?: Partial<ApiRequest>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'DELETE' });
  }

  // Core request method with retry logic
  async request<T = any>(requestConfig: ApiRequest): Promise<ApiResponse<T>> {
    const config = await this.processRequestInterceptors(requestConfig);
    const retryConfig = this.getRetryConfig(config);

    let lastError: ApiError;
    
    for (let attempt = 0; attempt <= retryConfig.retries; attempt++) {
      try {
        const response = await this.executeRequest<T>(config);
        return await this.processResponseInterceptors(response);
      } catch (error) {
        lastError = this.normalizeError(error, config);
        
        // Don't retry on the last attempt or if retry condition fails
        if (attempt === retryConfig.retries || !this.shouldRetry(lastError, retryConfig)) {
          break;
        }

        // Wait before retrying
        const delay = this.calculateRetryDelay(attempt, retryConfig);
        await this.sleep(delay);
      }
    }

    // Process error through interceptors
    return this.processErrorInterceptors(lastError!);
  }

  // Execute the actual HTTP request
  private async executeRequest<T>(config: ApiRequest): Promise<ApiResponse<T>> {
    const url = this.buildUrl(config.url, config.params);
    const headers = this.buildHeaders(config);
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout || this.config.timeout);

    try {
      const response = await fetch(url, {
        method: config.method,
        headers,
        body: config.data ? JSON.stringify(config.data) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const responseData = await this.parseResponse(response);
      
      if (!response.ok) {
        throw this.createApiError(response, responseData, config);
      }

      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: this.parseHeaders(response.headers),
        config,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  // Interceptor management
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  addErrorInterceptor(interceptor: ErrorInterceptor): void {
    this.errorInterceptors.push(interceptor);
  }

  // Process interceptors
  private async processRequestInterceptors(config: ApiRequest): Promise<ApiRequest> {
    let processedConfig = { ...config };
    
    for (const interceptor of this.requestInterceptors) {
      processedConfig = await interceptor(processedConfig);
    }
    
    return processedConfig;
  }

  private async processResponseInterceptors<T>(response: ApiResponse<T>): Promise<ApiResponse<T>> {
    let processedResponse = response;
    
    for (const interceptor of this.responseInterceptors) {
      processedResponse = await interceptor(processedResponse);
    }
    
    return processedResponse;
  }

  private async processErrorInterceptors(error: ApiError): Promise<never> {
    let processedError = error;
    
    for (const interceptor of this.errorInterceptors) {
      try {
        await interceptor(processedError);
      } catch (newError) {
        processedError = newError as ApiError;
      }
    }
    
    throw processedError;
  }

  // Helper methods
  private buildUrl(url: string, params?: Record<string, any>): string {
    const fullUrl = url.startsWith('http') ? url : `${this.config.baseURL}${url}`;
    
    if (!params) return fullUrl;
    
    const urlObj = new URL(fullUrl);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlObj.searchParams.append(key, String(value));
      }
    });
    
    return urlObj.toString();
  }

  private buildHeaders(config: ApiRequest): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      ...this.config.headers,
      ...config.headers,
    };
  }

  private async parseResponse(response: Response): Promise<any> {
    const contentType = response.headers.get('content-type');
    
    if (contentType?.includes('application/json')) {
      return response.json();
    } else if (contentType?.includes('text/')) {
      return response.text();
    } else {
      return response.blob();
    }
  }

  private parseHeaders(headers: Headers): Record<string, string> {
    const result: Record<string, string> = {};
    headers.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }

  private createApiError(response: Response, data: any, config: ApiRequest): ApiError {
    return {
      message: `Request failed with status ${response.status}`,
      status: response.status,
      code: data?.code || response.status.toString(),
      data,
      config,
    };
  }

  private normalizeError(error: any, config: ApiRequest): ApiError {
    if (error.name === 'AbortError') {
      return {
        message: 'Request timeout',
        code: 'TIMEOUT',
        config,
      };
    }
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        message: 'Network error',
        code: 'NETWORK_ERROR',
        config,
      };
    }
    
    return error as ApiError;
  }

  private getRetryConfig(config: ApiRequest): RetryConfig {
    return {
      retries: config.retries ?? this.config.retries,
      retryDelay: this.config.retryDelay,
      exponentialBackoff: true,
      maxRetryDelay: 30000,
      retryCondition: (error) => {
        // Retry on network errors, timeouts, and 5xx status codes
        return !error.status || 
               error.status >= 500 || 
               error.code === 'TIMEOUT' || 
               error.code === 'NETWORK_ERROR';
      },
    };
  }

  private shouldRetry(error: ApiError, retryConfig: RetryConfig): boolean {
    return retryConfig.retryCondition ? retryConfig.retryCondition(error) : true;
  }

  private calculateRetryDelay(attempt: number, retryConfig: RetryConfig): number {
    if (!retryConfig.exponentialBackoff) {
      return retryConfig.retryDelay;
    }
    
    const delay = retryConfig.retryDelay * Math.pow(2, attempt);
    return Math.min(delay, retryConfig.maxRetryDelay || 30000);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Default interceptors
  private addDefaultInterceptors(): void {
    // Request logging interceptor
    this.addRequestInterceptor((config) => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`API Request: ${config.method} ${config.url}`, config);
      }
      return config;
    });

    // Response logging interceptor
    this.addResponseInterceptor((response) => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`API Response: ${response.status} ${response.config.url}`, response);
      }
      return response;
    });

    // Error handling interceptor
    this.addErrorInterceptor(async (error) => {
      // Create standardized error using ErrorHandler
      const appError = ErrorHandler.handleApiError(
        error.config?.url || 'unknown',
        error.status || 0,
        error.data
      );

      // Log the error
      console.error('API Error:', appError);

      // Re-throw the original error for the caller to handle
      throw error;
    });
  }
}

// Create default API client instance
export const apiClient = new ApiClient({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
});

// Authentication interceptor
apiClient.addRequestInterceptor(async (config) => {
  if (!config.skipAuth) {
    // Add authentication token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers = {
        ...config.headers,
        'Authorization': `Bearer ${token}`,
      };
    }
  }
  return config;
});
