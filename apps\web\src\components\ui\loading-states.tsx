import * as React from "react"
import { cn } from "@/lib/utils"
import { LoadingIcon } from "@/components/ui/icon"
import { Typography } from "@/components/ui/typography"
import { VStack, HStack } from "@/components/ui/layout"

// Basic skeleton component
interface SkeletonProps {
  className?: string
  width?: string | number
  height?: string | number
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded'
  animation?: 'pulse' | 'wave' | 'none'
}

export function Skeleton({
  className,
  width,
  height,
  variant = 'rectangular',
  animation = 'pulse'
}: SkeletonProps) {
  const variantClasses = {
    text: 'h-4 rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-none',
    rounded: 'rounded-md'
  }

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-shimmer',
    none: ''
  }

  return (
    <div
      className={cn(
        "bg-muted",
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height
      }}
    />
  )
}

// Text skeleton with multiple lines
interface TextSkeletonProps {
  lines?: number
  className?: string
  lastLineWidth?: string
}

export function TextSkeleton({ 
  lines = 3, 
  className,
  lastLineWidth = '60%' 
}: TextSkeletonProps) {
  return (
    <VStack spacing="xs" className={className}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          width={index === lines - 1 ? lastLineWidth : '100%'}
          className="h-4"
        />
      ))}
    </VStack>
  )
}

// Card skeleton
interface CardSkeletonProps {
  showAvatar?: boolean
  showImage?: boolean
  lines?: number
  className?: string
}

export function CardSkeleton({ 
  showAvatar = false, 
  showImage = false, 
  lines = 3,
  className 
}: CardSkeletonProps) {
  return (
    <div className={cn("p-4 border border-border rounded-lg", className)}>
      <VStack spacing="md">
        {/* Header with avatar */}
        {showAvatar && (
          <HStack spacing="sm" align="center">
            <Skeleton variant="circular" width={40} height={40} />
            <VStack spacing="xs" className="flex-1">
              <Skeleton variant="text" width="40%" />
              <Skeleton variant="text" width="60%" />
            </VStack>
          </HStack>
        )}

        {/* Image */}
        {showImage && (
          <Skeleton variant="rounded" className="w-full h-48" />
        )}

        {/* Content */}
        <TextSkeleton lines={lines} />
      </VStack>
    </div>
  )
}

// Table skeleton
interface TableSkeletonProps {
  rows?: number
  columns?: number
  showHeader?: boolean
  className?: string
}

export function TableSkeleton({ 
  rows = 5, 
  columns = 4, 
  showHeader = true,
  className 
}: TableSkeletonProps) {
  return (
    <div className={cn("w-full", className)}>
      {/* Header */}
      {showHeader && (
        <div className="grid gap-4 p-4 border-b border-border" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton key={index} variant="text" width="80%" />
          ))}
        </div>
      )}

      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div 
          key={rowIndex} 
          className="grid gap-4 p-4 border-b border-border last:border-b-0" 
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} variant="text" width="90%" />
          ))}
        </div>
      ))}
    </div>
  )
}

// Loading spinner with message
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  message?: string
  className?: string
}

export function LoadingSpinner({ 
  size = 'md', 
  message,
  className 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  return (
    <VStack spacing="sm" align="center" className={cn("py-8", className)}>
      <LoadingIcon className={cn("animate-spin text-primary", sizeClasses[size])} />
      {message && (
        <Typography variant="body-sm" className="text-muted-foreground">
          {message}
        </Typography>
      )}
    </VStack>
  )
}

// Progress bar
interface ProgressBarProps {
  value: number
  max?: number
  label?: string
  showPercentage?: boolean
  variant?: 'default' | 'success' | 'warning' | 'error'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function ProgressBar({
  value,
  max = 100,
  label,
  showPercentage = true,
  variant = 'default',
  size = 'md',
  className
}: ProgressBarProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  
  const variantClasses = {
    default: 'bg-primary',
    success: 'bg-success',
    warning: 'bg-warning',
    error: 'bg-destructive'
  }

  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  }

  return (
    <VStack spacing="xs" className={className}>
      {(label || showPercentage) && (
        <HStack spacing="sm" className="justify-between items-center">
          {label && (
            <Typography variant="body-sm" className="text-foreground">
              {label}
            </Typography>
          )}
          {showPercentage && (
            <Typography variant="caption" className="text-muted-foreground">
              {Math.round(percentage)}%
            </Typography>
          )}
        </HStack>
      )}
      
      <div className={cn("w-full bg-muted rounded-full", sizeClasses[size])}>
        <div
          className={cn(
            "rounded-full transition-all duration-300 ease-out",
            sizeClasses[size],
            variantClasses[variant]
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </VStack>
  )
}

// Circular progress
interface CircularProgressProps {
  value: number
  max?: number
  size?: number
  strokeWidth?: number
  label?: string
  showPercentage?: boolean
  variant?: 'default' | 'success' | 'warning' | 'error'
  className?: string
}

export function CircularProgress({
  value,
  max = 100,
  size = 64,
  strokeWidth = 4,
  label,
  showPercentage = true,
  variant = 'default',
  className
}: CircularProgressProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference

  const variantClasses = {
    default: 'stroke-primary',
    success: 'stroke-success',
    warning: 'stroke-warning',
    error: 'stroke-destructive'
  }

  return (
    <VStack spacing="xs" align="center" className={className}>
      <div className="relative">
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="none"
            className="text-muted"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
            fill="none"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className={cn("transition-all duration-300 ease-out", variantClasses[variant])}
          />
        </svg>
        
        {/* Center content */}
        {showPercentage && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Typography variant="caption" className="font-medium">
              {Math.round(percentage)}%
            </Typography>
          </div>
        )}
      </div>
      
      {label && (
        <Typography variant="caption" className="text-muted-foreground text-center">
          {label}
        </Typography>
      )}
    </VStack>
  )
}

// Loading overlay
interface LoadingOverlayProps {
  isLoading: boolean
  children: React.ReactNode
  message?: string
  className?: string
}

export function LoadingOverlay({ 
  isLoading, 
  children, 
  message = "Loading...",
  className 
}: LoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <LoadingSpinner message={message} />
        </div>
      )}
    </div>
  )
}

// Pulse loading animation
interface PulseLoaderProps {
  count?: number
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function PulseLoader({ 
  count = 3, 
  size = 'md',
  className 
}: PulseLoaderProps) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  }

  return (
    <HStack spacing="xs" align="center" className={className}>
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className={cn(
            "bg-primary rounded-full animate-pulse",
            sizeClasses[size]
          )}
          style={{
            animationDelay: `${index * 0.2}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </HStack>
  )
}

// Shimmer effect for custom content
interface ShimmerProps {
  children: React.ReactNode
  className?: string
}

export function Shimmer({ children, className }: ShimmerProps) {
  return (
    <div className={cn("relative overflow-hidden", className)}>
      {children}
      <div className="absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/20 to-transparent" />
    </div>
  )
}

// Monaco Editor skeleton loader with exact dimensions to prevent CLS
interface MonacoEditorSkeletonProps {
  height?: string | number
  className?: string
}

export function MonacoEditorSkeleton({
  height = 400,
  className
}: MonacoEditorSkeletonProps) {
  const heightStyle = typeof height === 'number' ? `${height}px` : height

  return (
    <div
      className={cn(
        "monaco-editor-container bg-background border border-border rounded-lg overflow-hidden",
        className
      )}
      style={{ height: heightStyle, minHeight: heightStyle }}
    >
      <div className="h-full flex flex-col">
        {/* Editor toolbar skeleton */}
        <div className="h-8 bg-muted border-b border-border flex items-center px-3 space-x-2">
          <Skeleton variant="rounded" width={64} height={16} />
          <Skeleton variant="rounded" width={48} height={16} />
          <div className="flex-1"></div>
          <Skeleton variant="rounded" width={32} height={16} />
        </div>

        {/* Editor content skeleton */}
        <div className="flex-1 p-4 space-y-3 overflow-hidden">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="flex items-center space-x-2">
              <div className="w-6 h-4 bg-muted-foreground/10 rounded text-xs flex items-center justify-center text-muted-foreground">
                {i + 1}
              </div>
              <Skeleton
                variant="text"
                height={16}
                width={`${Math.random() * 60 + 20}%`}
                animation="pulse"
                className="animate-pulse"
                style={{ animationDelay: `${i * 100}ms` }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
