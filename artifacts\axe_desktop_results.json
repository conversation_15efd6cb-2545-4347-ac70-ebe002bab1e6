{"accessibility": {"Desktop": {"score": 85, "violations": {"critical": 1, "serious": 1, "moderate": 0, "minor": 0, "total": 2}, "details": [{"id": "aria-valid-attr-value", "impact": "critical", "description": "Ensure all ARIA attributes have valid values", "help": "ARIA attributes must conform to valid values", "nodes": 2}, {"id": "color-contrast", "impact": "serious", "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "nodes": 10}]}}, "focusTests": [{"buttonIndex": 0, "focused": true, "visibleFocus": true}, {"buttonIndex": 1, "focused": true, "visibleFocus": true}, {"buttonIndex": 2, "focused": true, "visibleFocus": true}, {"buttonIndex": 3, "focused": true, "visibleFocus": true}, {"buttonIndex": 4, "focused": true, "visibleFocus": true}], "analysis": {"complianceLevel": "BELOW_TARGET", "targetScore": 97, "actualScore": 85, "gap": -12, "riskLevel": "MEDIUM", "criticalIssues": ["Invalid ARIA attribute values affecting 2 elements", "Color contrast violations affecting 10 elements"], "recommendations": ["Fix ARIA attribute values to conform to specification", "Improve color contrast ratios to meet WCAG 2.2 AA standards", "Test with screen readers to validate ARIA implementation", "Implement automated accessibility testing in CI/CD"]}, "timestamp": "2025-06-19T20:49:09.953Z"}