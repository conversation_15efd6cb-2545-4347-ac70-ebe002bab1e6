
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/providers</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/providers</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">31.21% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>128/410</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.5% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>37/200</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.61% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>26/73</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">31.76% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>128/403</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="AgentProvider.ts"><a href="AgentProvider.ts.html">AgentProvider.ts</a></td>
	<td data-value="75" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="36" class="abs medium">27/36</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="71.42" class="pct medium">71.42%</td>
	<td data-value="14" class="abs medium">10/14</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="36" class="abs medium">27/36</td>
	</tr>

<tr>
	<td class="file low" data-value="AnthropicProvider.ts"><a href="AnthropicProvider.ts.html">AnthropicProvider.ts</a></td>
	<td data-value="7.75" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.75" class="pct low">7.75%</td>
	<td data-value="129" class="abs low">10/129</td>
	<td data-value="4.91" class="pct low">4.91%</td>
	<td data-value="61" class="abs low">3/61</td>
	<td data-value="4.34" class="pct low">4.34%</td>
	<td data-value="23" class="abs low">1/23</td>
	<td data-value="8.13" class="pct low">8.13%</td>
	<td data-value="123" class="abs low">10/123</td>
	</tr>

<tr>
	<td class="file low" data-value="OpenAIProvider.ts"><a href="OpenAIProvider.ts.html">OpenAIProvider.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="97" class="abs low">0/97</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="53" class="abs low">0/53</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="97" class="abs low">0/97</td>
	</tr>

<tr>
	<td class="file low" data-value="ProviderFactory.ts"><a href="ProviderFactory.ts.html">ProviderFactory.ts</a></td>
	<td data-value="18" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 18%"></div><div class="cover-empty" style="width: 82%"></div></div>
	</td>
	<td data-value="18" class="pct low">18%</td>
	<td data-value="50" class="abs low">9/50</td>
	<td data-value="3.22" class="pct low">3.22%</td>
	<td data-value="31" class="abs low">1/31</td>
	<td data-value="30" class="pct low">30%</td>
	<td data-value="10" class="abs low">3/10</td>
	<td data-value="18.36" class="pct low">18.36%</td>
	<td data-value="49" class="abs low">9/49</td>
	</tr>

<tr>
	<td class="file medium" data-value="VertexAIProvider.ts"><a href="VertexAIProvider.ts.html">VertexAIProvider.ts</a></td>
	<td data-value="83.67" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.67" class="pct medium">83.67%</td>
	<td data-value="98" class="abs medium">82/98</td>
	<td data-value="61.11" class="pct medium">61.11%</td>
	<td data-value="54" class="abs medium">33/54</td>
	<td data-value="92.3" class="pct high">92.3%</td>
	<td data-value="13" class="abs high">12/13</td>
	<td data-value="83.67" class="pct medium">83.67%</td>
	<td data-value="98" class="abs medium">82/98</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-29T04:58:16.161Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    