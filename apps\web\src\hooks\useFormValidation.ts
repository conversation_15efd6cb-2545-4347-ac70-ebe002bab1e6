import { useState, useCallback, useEffect } from 'react';

// Validation rule types
export type ValidationRule = {
  required?: boolean | string;
  minLength?: number | { value: number; message: string };
  maxLength?: number | { value: number; message: string };
  pattern?: RegExp | { value: RegExp; message: string };
  email?: boolean | string;
  url?: boolean | string;
  number?: boolean | string;
  min?: number | { value: number; message: string };
  max?: number | { value: number; message: string };
  custom?: (value: any) => string | boolean;
  match?: string; // Field name to match against
};

export type FieldConfig = {
  rules?: ValidationRule;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
};

export type FormConfig<T> = {
  [K in keyof T]?: FieldConfig;
};

export type FieldError = {
  type: string;
  message: string;
};

export type FormState<T> = {
  values: T;
  errors: Partial<Record<keyof T, FieldError>>;
  touched: Partial<Record<keyof T, boolean>>;
  isValid: boolean;
  isSubmitting: boolean;
  isValidating: boolean;
  submitCount: number;
};

export type FormActions<T> = {
  setValue: (field: keyof T, value: any) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: keyof T, error: FieldError | null) => void;
  setErrors: (errors: Partial<Record<keyof T, FieldError>>) => void;
  setTouched: (field: keyof T, touched: boolean) => void;
  validateField: (field: keyof T) => Promise<boolean>;
  validateForm: () => Promise<boolean>;
  resetForm: (values?: Partial<T>) => void;
  resetField: (field: keyof T) => void;
  handleSubmit: (onSubmit: (values: T) => Promise<void> | void) => (e?: React.FormEvent) => Promise<void>;
  getFieldProps: (field: keyof T) => {
    value: any;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
    onBlur: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
    error: FieldError | undefined;
    touched: boolean;
  };
};

// Validation utilities
const validateRule = (value: any, rule: ValidationRule, allValues: any): FieldError | null => {
  // Required validation
  if (rule.required) {
    const isEmpty = value === undefined || value === null || value === '' || 
                   (Array.isArray(value) && value.length === 0);
    if (isEmpty) {
      const message = typeof rule.required === 'string' ? rule.required : 'This field is required';
      return { type: 'required', message };
    }
  }

  // Skip other validations if value is empty and not required
  if (!value && !rule.required) return null;

  // String length validations
  if (typeof value === 'string') {
    if (rule.minLength) {
      const minLength = typeof rule.minLength === 'number' ? rule.minLength : rule.minLength.value;
      const message = typeof rule.minLength === 'object' ? rule.minLength.message : 
                     `Must be at least ${minLength} characters`;
      if (value.length < minLength) {
        return { type: 'minLength', message };
      }
    }

    if (rule.maxLength) {
      const maxLength = typeof rule.maxLength === 'number' ? rule.maxLength : rule.maxLength.value;
      const message = typeof rule.maxLength === 'object' ? rule.maxLength.message : 
                     `Must be no more than ${maxLength} characters`;
      if (value.length > maxLength) {
        return { type: 'maxLength', message };
      }
    }
  }

  // Pattern validation
  if (rule.pattern) {
    const pattern = rule.pattern instanceof RegExp ? rule.pattern : rule.pattern.value;
    const message = rule.pattern instanceof RegExp ? 'Invalid format' : rule.pattern.message;
    if (!pattern.test(String(value))) {
      return { type: 'pattern', message };
    }
  }

  // Email validation
  if (rule.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const message = typeof rule.email === 'string' ? rule.email : 'Please enter a valid email address';
    if (!emailRegex.test(String(value))) {
      return { type: 'email', message };
    }
  }

  // URL validation
  if (rule.url) {
    try {
      new URL(String(value));
    } catch {
      const message = typeof rule.url === 'string' ? rule.url : 'Please enter a valid URL';
      return { type: 'url', message };
    }
  }

  // Number validation
  if (rule.number) {
    const numValue = Number(value);
    if (isNaN(numValue)) {
      const message = typeof rule.number === 'string' ? rule.number : 'Please enter a valid number';
      return { type: 'number', message };
    }

    // Min/max for numbers
    if (rule.min !== undefined) {
      const min = typeof rule.min === 'number' ? rule.min : rule.min.value;
      const message = typeof rule.min === 'object' ? rule.min.message : `Must be at least ${min}`;
      if (numValue < min) {
        return { type: 'min', message };
      }
    }

    if (rule.max !== undefined) {
      const max = typeof rule.max === 'number' ? rule.max : rule.max.value;
      const message = typeof rule.max === 'object' ? rule.max.message : `Must be no more than ${max}`;
      if (numValue > max) {
        return { type: 'max', message };
      }
    }
  }

  // Match validation (for password confirmation, etc.)
  if (rule.match) {
    const matchValue = allValues[rule.match];
    if (value !== matchValue) {
      return { type: 'match', message: 'Values do not match' };
    }
  }

  // Custom validation
  if (rule.custom) {
    const result = rule.custom(value);
    if (typeof result === 'string') {
      return { type: 'custom', message: result };
    }
    if (result === false) {
      return { type: 'custom', message: 'Invalid value' };
    }
  }

  return null;
};

// Debounce utility
const useDebounce = (callback: Function, delay: number) => {
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    
    const timer = setTimeout(() => {
      callback(...args);
    }, delay);
    
    setDebounceTimer(timer);
  }, [callback, delay, debounceTimer]);
};

// Main form validation hook
export const useFormValidation = <T extends Record<string, any>>(
  initialValues: T,
  config: FormConfig<T> = {}
): FormState<T> & FormActions<T> => {
  const [state, setState] = useState<FormState<T>>({
    values: initialValues,
    errors: {},
    touched: {},
    isValid: true,
    isSubmitting: false,
    isValidating: false,
    submitCount: 0,
  });

  // Validate a single field
  const validateField = useCallback(async (field: keyof T): Promise<boolean> => {
    const fieldConfig = config[field];
    if (!fieldConfig?.rules) return true;

    setState(prev => ({ ...prev, isValidating: true }));

    const value = state.values[field];
    const error = validateRule(value, fieldConfig.rules, state.values);

    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [field]: error || undefined,
      },
      isValidating: false,
    }));

    return !error;
  }, [config, state.values]);

  // Debounced field validation
  const debouncedValidateField = useCallback((field: keyof T) => {
    const fieldConfig = config[field];
    const debounceMs = fieldConfig?.debounceMs || 300;
    
    return useDebounce(() => validateField(field), debounceMs);
  }, [config, validateField]);

  // Validate entire form
  const validateForm = useCallback(async (): Promise<boolean> => {
    setState(prev => ({ ...prev, isValidating: true }));

    const errors: Partial<Record<keyof T, FieldError>> = {};
    let isValid = true;

    for (const field in config) {
      const fieldConfig = config[field];
      if (fieldConfig?.rules) {
        const value = state.values[field];
        const error = validateRule(value, fieldConfig.rules, state.values);
        if (error) {
          errors[field] = error;
          isValid = false;
        }
      }
    }

    setState(prev => ({
      ...prev,
      errors,
      isValid,
      isValidating: false,
    }));

    return isValid;
  }, [config, state.values]);

  // Update form validity when errors change
  useEffect(() => {
    const hasErrors = Object.keys(state.errors).some(key => state.errors[key as keyof T]);
    setState(prev => ({ ...prev, isValid: !hasErrors }));
  }, [state.errors]);

  // Actions
  const setValue = useCallback((field: keyof T, value: any) => {
    setState(prev => ({
      ...prev,
      values: { ...prev.values, [field]: value },
    }));

    // Validate on change if configured
    const fieldConfig = config[field];
    if (fieldConfig?.validateOnChange && state.touched[field]) {
      debouncedValidateField(field)();
    }
  }, [config, state.touched, debouncedValidateField]);

  const setValues = useCallback((values: Partial<T>) => {
    setState(prev => ({
      ...prev,
      values: { ...prev.values, ...values },
    }));
  }, []);

  const setError = useCallback((field: keyof T, error: FieldError | null) => {
    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [field]: error || undefined,
      },
    }));
  }, []);

  const setErrors = useCallback((errors: Partial<Record<keyof T, FieldError>>) => {
    setState(prev => ({ ...prev, errors }));
  }, []);

  const setTouched = useCallback((field: keyof T, touched: boolean) => {
    setState(prev => ({
      ...prev,
      touched: { ...prev.touched, [field]: touched },
    }));
  }, []);

  const resetForm = useCallback((values?: Partial<T>) => {
    setState({
      values: { ...initialValues, ...values },
      errors: {},
      touched: {},
      isValid: true,
      isSubmitting: false,
      isValidating: false,
      submitCount: 0,
    });
  }, [initialValues]);

  const resetField = useCallback((field: keyof T) => {
    setState(prev => ({
      ...prev,
      values: { ...prev.values, [field]: initialValues[field] },
      errors: { ...prev.errors, [field]: undefined },
      touched: { ...prev.touched, [field]: false },
    }));
  }, [initialValues]);

  const handleSubmit = useCallback((onSubmit: (values: T) => Promise<void> | void) => {
    return async (e?: React.FormEvent) => {
      e?.preventDefault();

      setState(prev => ({ 
        ...prev, 
        isSubmitting: true, 
        submitCount: prev.submitCount + 1 
      }));

      // Mark all fields as touched
      const allTouched = Object.keys(config).reduce((acc, key) => {
        acc[key as keyof T] = true;
        return acc;
      }, {} as Partial<Record<keyof T, boolean>>);

      setState(prev => ({ ...prev, touched: { ...prev.touched, ...allTouched } }));

      // Validate form
      const isValid = await validateForm();

      if (isValid) {
        try {
          await onSubmit(state.values);
        } catch (error) {
          console.error('Form submission error:', error);
        }
      }

      setState(prev => ({ ...prev, isSubmitting: false }));
    };
  }, [config, validateForm, state.values]);

  const getFieldProps = useCallback((field: keyof T) => {
    return {
      value: state.values[field] || '',
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setValue(field, e.target.value);
      },
      onBlur: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setTouched(field, true);
        
        // Validate on blur if configured
        const fieldConfig = config[field];
        if (fieldConfig?.validateOnBlur) {
          validateField(field);
        }
      },
      error: state.errors[field],
      touched: state.touched[field] || false,
    };
  }, [state.values, state.errors, state.touched, setValue, setTouched, validateField, config]);

  return {
    ...state,
    setValue,
    setValues,
    setError,
    setErrors,
    setTouched,
    validateField,
    validateForm,
    resetForm,
    resetField,
    handleSubmit,
    getFieldProps,
  };
};

export default useFormValidation;
