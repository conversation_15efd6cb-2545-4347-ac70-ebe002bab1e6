const API_URL = 'http://localhost:3001/api/reactor'; // Assuming the API is on port 3001

// Types
export interface LoopProgress {
  iteration: number;
  phase: 'planning' | 'critiquing' | 'implementing';
  score: number;
  message: string;
}

export interface LoopResult {
  success: boolean;
  iterations: number;
  finalScore: number;
  output: string;
  error?: string;
}

// API functions
export async function startTask(task: string) {
    const response = await fetch(`${API_URL}/task/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ task }),
    });
    return response.json();
}

export async function getTaskStatus(taskId: string) {
    const response = await fetch(`${API_URL}/task/status/${taskId}`);
    return response.json();
}

export async function stopTask(taskId: string) {
    const response = await fetch(`${API_URL}/task/stop/${taskId}`, {
        method: 'POST',
    });
    return response.json();
}

// Reactor API object
export const reactorApi = {
  startTask,
  getTaskStatus,
  stopTask
};