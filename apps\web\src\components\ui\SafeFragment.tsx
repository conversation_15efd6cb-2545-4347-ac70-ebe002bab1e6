import React from 'react';

/**
 * SafeFragment - A wrapper around React.Fragment that filters out invalid props
 * 
 * This component addresses warnings about invalid props being passed to React.Fragment
 * in development environments that inject tracking attributes like 'data-lov-id'.
 * 
 * React.Fragment only accepts 'key' and 'children' props, so we filter out any others.
 */

interface SafeFragmentProps {
  children: React.ReactNode;
  key?: React.Key;
  [key: string]: any; // Allow any props but filter them out
}

export function SafeFragment({ children, key, ...otherProps }: SafeFragmentProps) {
  // Only pass through the props that React.Fragment accepts
  const fragmentProps: { children: React.ReactNode; key?: React.Key } = { children };
  
  if (key !== undefined) {
    fragmentProps.key = key;
  }

  // Log filtered props in development for debugging
  if (process.env.NODE_ENV === 'development' && Object.keys(otherProps).length > 0) {
    const filteredProps = Object.keys(otherProps);
    if (filteredProps.some(prop => prop.startsWith('data-lov'))) {
      // Silently filter out development environment props
    } else {
      console.debug('SafeFragment: Filtered props:', filteredProps);
    }
  }

  return React.createElement(React.Fragment, fragmentProps);
}

export default SafeFragment;
