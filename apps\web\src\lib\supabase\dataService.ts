import { supabase } from '@/lib/supabase';
import type { 
  Transformation, 
  AgentLog, 
  UserSettings, 
  SystemHealth,
  ProjectMetrics,
  NotificationData 
} from './types';

export class SupabaseDataService {
  // Transformations
  async createTransformation(data: Omit<Transformation, 'id' | 'created_at' | 'updated_at'>): Promise<Transformation> {
    const { data: transformation, error } = await supabase
      .from('transformations')
      .insert(data)
      .select()
      .single();

    if (error) throw new Error(`Failed to create transformation: ${error.message}`);
    return transformation;
  }

  async getTransformation(id: string): Promise<Transformation | null> {
    const { data, error } = await supabase
      .from('transformations')
      .select('*')
      .eq('id', id)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to get transformation: ${error.message}`);
    }
    return data;
  }

  async getUserTransformations(userId: string, limit = 50): Promise<Transformation[]> {
    const { data, error } = await supabase
      .from('transformations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw new Error(`Failed to get user transformations: ${error.message}`);
    return data || [];
  }

  async updateTransformation(id: string, updates: Partial<Transformation>): Promise<Transformation> {
    const { data, error } = await supabase
      .from('transformations')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw new Error(`Failed to update transformation: ${error.message}`);
    return data;
  }

  // Agent Logs
  async createAgentLog(data: Omit<AgentLog, 'id' | 'created_at'>): Promise<AgentLog> {
    const { data: log, error } = await supabase
      .from('agent_logs')
      .insert(data)
      .select()
      .single();

    if (error) throw new Error(`Failed to create agent log: ${error.message}`);
    return log;
  }

  async getTransformationLogs(transformationId: string): Promise<AgentLog[]> {
    const { data, error } = await supabase
      .from('agent_logs')
      .select('*')
      .eq('transformation_id', transformationId)
      .order('iteration', { ascending: true });

    if (error) throw new Error(`Failed to get transformation logs: ${error.message}`);
    return data || [];
  }

  // User Settings
  async getUserSettings(userId: string): Promise<UserSettings | null> {
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to get user settings: ${error.message}`);
    }
    return data;
  }

  async upsertUserSettings(userId: string, settings: Partial<UserSettings>): Promise<UserSettings> {
    const { data, error } = await supabase
      .from('settings')
      .upsert({
        user_id: userId,
        ...settings,
        updated_at: new Date().toISOString()
      }, { onConflict: 'user_id' })
      .select()
      .single();

    if (error) throw new Error(`Failed to upsert user settings: ${error.message}`);
    return data;
  }

  // System Health
  async recordSystemHealth(data: Omit<SystemHealth, 'id' | 'timestamp'>): Promise<SystemHealth> {
    const { data: health, error } = await supabase
      .from('system_health')
      .insert({
        ...data,
        timestamp: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to record system health: ${error.message}`);
    return health;
  }

  async getSystemHealthHistory(hours = 24): Promise<SystemHealth[]> {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000).toISOString();
    
    const { data, error } = await supabase
      .from('system_health')
      .select('*')
      .gte('timestamp', since)
      .order('timestamp', { ascending: false });

    if (error) throw new Error(`Failed to get system health history: ${error.message}`);
    return data || [];
  }

  // Project Metrics
  async recordProjectMetrics(data: Omit<ProjectMetrics, 'id' | 'timestamp'>): Promise<ProjectMetrics> {
    const { data: metrics, error } = await supabase
      .from('project_metrics')
      .insert({
        ...data,
        timestamp: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to record project metrics: ${error.message}`);
    return metrics;
  }

  async getProjectMetrics(userId: string, days = 30): Promise<ProjectMetrics[]> {
    const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
    
    const { data, error } = await supabase
      .from('project_metrics')
      .select('*')
      .eq('user_id', userId)
      .gte('timestamp', since)
      .order('timestamp', { ascending: false });

    if (error) throw new Error(`Failed to get project metrics: ${error.message}`);
    return data || [];
  }

  // Notifications
  async createNotification(data: Omit<NotificationData, 'id' | 'created_at' | 'updated_at'>): Promise<NotificationData> {
    const { data: notification, error } = await supabase
      .from('notifications')
      .insert(data)
      .select()
      .single();

    if (error) throw new Error(`Failed to create notification: ${error.message}`);
    return notification;
  }

  async getUserNotifications(userId: string, limit = 50): Promise<NotificationData[]> {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw new Error(`Failed to get user notifications: ${error.message}`);
    return data || [];
  }

  async markNotificationRead(id: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({ 
        read: true, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', id);

    if (error) throw new Error(`Failed to mark notification as read: ${error.message}`);
  }

  // Real-time subscriptions
  subscribeToTransformations(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('transformations')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'transformations',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe();
  }

  subscribeToAgentLogs(transformationId: string, callback: (payload: any) => void) {
    return supabase
      .channel('agent_logs')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'agent_logs',
          filter: `transformation_id=eq.${transformationId}`
        },
        callback
      )
      .subscribe();
  }

  subscribeToNotifications(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe();
  }

  subscribeToSystemHealth(callback: (payload: any) => void) {
    return supabase
      .channel('system_health')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'system_health'
        },
        callback
      )
      .subscribe();
  }

  // Utility methods
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw new Error(`Failed to get current user: ${error.message}`);
    return user;
  }

  async getUserProfile(userId: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to get user profile: ${error.message}`);
    }
    return data;
  }

  async upsertUserProfile(userId: string, profile: any) {
    const { data, error } = await supabase
      .from('profiles')
      .upsert({
        id: userId,
        ...profile,
        updated_at: new Date().toISOString()
      }, { onConflict: 'id' })
      .select()
      .single();

    if (error) throw new Error(`Failed to upsert user profile: ${error.message}`);
    return data;
  }

  // Analytics and aggregations
  async getUserStats(userId: string) {
    const [transformations, totalCost, avgScore] = await Promise.all([
      this.getUserTransformations(userId, 1000),
      this.getTotalUserCost(userId),
      this.getAverageUserScore(userId)
    ]);

    return {
      totalTransformations: transformations.length,
      completedTransformations: transformations.filter(t => t.status === 'completed').length,
      totalCost,
      averageScore: avgScore,
      recentActivity: transformations.slice(0, 10)
    };
  }

  private async getTotalUserCost(userId: string): Promise<number> {
    const { data, error } = await supabase
      .from('transformations')
      .select('cost_usd')
      .eq('user_id', userId);

    if (error) return 0;
    return data?.reduce((sum, t) => sum + (t.cost_usd || 0), 0) || 0;
  }

  private async getAverageUserScore(userId: string): Promise<number> {
    const { data, error } = await supabase
      .from('transformations')
      .select('final_score')
      .eq('user_id', userId)
      .not('final_score', 'is', null);

    if (error || !data?.length) return 0;
    const sum = data.reduce((acc, t) => acc + (t.final_score || 0), 0);
    return sum / data.length;
  }
}

// Singleton instance
export const dataService = new SupabaseDataService();
