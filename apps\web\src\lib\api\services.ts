import { apiClient, ApiClient } from './apiClient';
import { supabase } from '@/lib/supabase';
import type { 
  Transformation, 
  AgentLog, 
  UserSettings, 
  SystemHealth,
  ProjectMetrics 
} from '@/lib/supabase/types';

// GitHub API Service
export class GitHubApiService {
  private client: ApiClient;

  constructor() {
    this.client = new ApiClient({
      baseURL: 'https://api.github.com',
      timeout: 15000,
      retries: 2,
      headers: {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Code-Alchemy-Reactor/1.0',
      },
    });

    // Add GitHub-specific auth interceptor
    this.client.addRequestInterceptor(async (config) => {
      const token = await this.getGitHubToken();
      if (token) {
        config.headers = {
          ...config.headers,
          'Authorization': `Bearer ${token}`,
        };
      }
      return config;
    });
  }

  private async getGitHubToken(): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data, error } = await supabase
        .from('github_tokens')
        .select('access_token_encrypted')
        .eq('user_id', user.id)
        .single();

      if (error || !data) return null;

      const { data: decryptedToken } = await supabase.rpc('decrypt_secret', {
        encrypted_secret: data.access_token_encrypted
      });

      return decryptedToken;
    } catch (error) {
      console.error('Error getting GitHub token:', error);
      return null;
    }
  }

  async getUser() {
    return this.client.get('/user');
  }

  async getRepositories(params?: { type?: string; sort?: string; per_page?: number }) {
    return this.client.get('/user/repos', { params });
  }

  async getRepository(owner: string, repo: string) {
    return this.client.get(`/repos/${owner}/${repo}`);
  }

  async getRepositoryContents(owner: string, repo: string, path: string = '') {
    return this.client.get(`/repos/${owner}/${repo}/contents/${path}`);
  }

  async createPullRequest(owner: string, repo: string, data: {
    title: string;
    body: string;
    head: string;
    base: string;
  }) {
    return this.client.post(`/repos/${owner}/${repo}/pulls`, data);
  }

  async getRateLimit() {
    return this.client.get('/rate_limit');
  }
}

// Transformation API Service
export class TransformationApiService {
  async createTransformation(data: {
    prompt: string;
    code: string;
    maxIterations?: number;
    scoreThreshold?: number;
  }) {
    return apiClient.post('/transformations', data);
  }

  async getTransformation(id: string) {
    return apiClient.get(`/transformations/${id}`);
  }

  async getUserTransformations(params?: {
    limit?: number;
    offset?: number;
    status?: string;
  }) {
    return apiClient.get('/transformations', { params });
  }

  async stopTransformation(id: string) {
    return apiClient.post(`/transformations/${id}/stop`);
  }

  async getTransformationLogs(id: string) {
    return apiClient.get(`/transformations/${id}/logs`);
  }

  async createPullRequest(transformationId: string, data: {
    repository: string;
    branch: string;
    title: string;
    description: string;
  }) {
    return apiClient.post(`/transformations/${transformationId}/pr`, data);
  }
}

// AI Provider API Service
export class AIProviderApiService {
  async testConnection(provider: string, credentials: any) {
    return apiClient.post('/ai/test-connection', {
      provider,
      credentials,
    });
  }

  async getProviderHealth() {
    return apiClient.get('/ai/health');
  }

  async getUsageStats(timeframe?: string) {
    return apiClient.get('/ai/usage', {
      params: { timeframe },
    });
  }

  async getCostEstimate(data: {
    prompt: string;
    model: string;
    maxTokens: number;
  }) {
    return apiClient.post('/ai/cost-estimate', data);
  }
}

// System Health API Service
export class SystemHealthApiService {
  async getSystemHealth() {
    return apiClient.get('/system/health');
  }

  async getSystemMetrics(timeframe?: string) {
    return apiClient.get('/system/metrics', {
      params: { timeframe },
    });
  }

  async getPerformanceStats() {
    return apiClient.get('/system/performance');
  }

  async getErrorLogs(params?: {
    level?: string;
    limit?: number;
    since?: string;
  }) {
    return apiClient.get('/system/errors', { params });
  }
}

// User Management API Service
export class UserApiService {
  async getCurrentUser() {
    return apiClient.get('/user/profile');
  }

  async updateProfile(data: {
    full_name?: string;
    avatar_url?: string;
    github_username?: string;
  }) {
    return apiClient.patch('/user/profile', data);
  }

  async getUserStats() {
    return apiClient.get('/user/stats');
  }

  async getUserActivity(params?: {
    limit?: number;
    type?: string;
  }) {
    return apiClient.get('/user/activity', { params });
  }

  async deleteAccount() {
    return apiClient.delete('/user/account');
  }
}

// Notification API Service
export class NotificationApiService {
  async getNotifications(params?: {
    limit?: number;
    unread_only?: boolean;
  }) {
    return apiClient.get('/notifications', { params });
  }

  async markAsRead(id: string) {
    return apiClient.patch(`/notifications/${id}/read`);
  }

  async markAllAsRead() {
    return apiClient.patch('/notifications/read-all');
  }

  async deleteNotification(id: string) {
    return apiClient.delete(`/notifications/${id}`);
  }

  async updatePreferences(preferences: {
    email_enabled?: boolean;
    push_enabled?: boolean;
    types?: string[];
  }) {
    return apiClient.patch('/notifications/preferences', preferences);
  }
}

// Analytics API Service
export class AnalyticsApiService {
  async getDashboardStats(timeframe?: string) {
    return apiClient.get('/analytics/dashboard', {
      params: { timeframe },
    });
  }

  async getUsageAnalytics(params?: {
    timeframe?: string;
    granularity?: 'hour' | 'day' | 'week' | 'month';
  }) {
    return apiClient.get('/analytics/usage', { params });
  }

  async getCostAnalytics(params?: {
    timeframe?: string;
    breakdown?: 'provider' | 'model' | 'user';
  }) {
    return apiClient.get('/analytics/costs', { params });
  }

  async getPerformanceAnalytics(params?: {
    timeframe?: string;
    metric?: 'response_time' | 'success_rate' | 'throughput';
  }) {
    return apiClient.get('/analytics/performance', { params });
  }
}

// Export service instances
export const githubApi = new GitHubApiService();
export const transformationApi = new TransformationApiService();
export const aiProviderApi = new AIProviderApiService();
export const systemHealthApi = new SystemHealthApiService();
export const userApi = new UserApiService();
export const notificationApi = new NotificationApiService();
export const analyticsApi = new AnalyticsApiService();

// Unified API service that combines all services
export class ApiService {
  github = githubApi;
  transformations = transformationApi;
  aiProviders = aiProviderApi;
  systemHealth = systemHealthApi;
  user = userApi;
  notifications = notificationApi;
  analytics = analyticsApi;

  // Utility methods
  async healthCheck() {
    try {
      const response = await apiClient.get('/health');
      return response.data;
    } catch (error) {
      throw new Error('API health check failed');
    }
  }

  async getApiInfo() {
    try {
      const response = await apiClient.get('/info');
      return response.data;
    } catch (error) {
      throw new Error('Failed to get API info');
    }
  }

  // Batch operations
  async batchRequest(requests: Array<{
    id: string;
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    url: string;
    data?: any;
  }>) {
    return apiClient.post('/batch', { requests });
  }
}

// Export singleton instance
export const api = new ApiService();
