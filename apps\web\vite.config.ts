import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // Performance optimizations
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production',
      },
    },
    // Code splitting configuration
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks for large libraries
          if (id.includes('@monaco-editor/react')) return 'monaco';
          if (id.includes('recharts')) return 'charts';
          if (id.includes('@supabase/supabase-js')) return 'supabase';

          // React ecosystem
          if (id.includes('react-router')) return 'react-router';
          if (id.includes('react-query') || id.includes('@tanstack/react-query')) return 'react-query';
          if (id.includes('react') && id.includes('node_modules') && !id.includes('@')) return 'react-core';

          // Radix UI components (split into smaller chunks)
          if (id.includes('@radix-ui/react-dialog') || id.includes('@radix-ui/react-dropdown-menu')) return 'radix-overlays';
          if (id.includes('@radix-ui/react-tabs') || id.includes('@radix-ui/react-accordion')) return 'radix-navigation';
          if (id.includes('@radix-ui/')) return 'radix-core';

          // Utility libraries
          if (id.includes('date-fns') || id.includes('clsx') ||
              id.includes('class-variance-authority') ||
              id.includes('tailwind-merge')) return 'utils';

          // Lucide icons (separate chunk)
          if (id.includes('lucide-react')) return 'icons';

          // Split large pages into separate chunks
          if (id.includes('/pages/Dashboard')) return 'page-dashboard';
          if (id.includes('/pages/Settings')) return 'page-settings';
          if (id.includes('/pages/History')) return 'page-history';
          if (id.includes('/pages/MonitoringDashboard')) return 'page-monitoring';

          // Split component groups more granularly
          if (id.includes('/components/dashboard/')) return 'dashboard-widgets';
          if (id.includes('/components/editor/')) return 'editor-components';
          if (id.includes('/components/charts/')) return 'chart-components';
          if (id.includes('/components/ui/')) return 'ui-components';
          if (id.includes('/hooks/')) return 'hooks';
          if (id.includes('/services/')) return 'services';
          if (id.includes('/lib/')) return 'lib-utils';

          // Split heavy individual components
          if (id.includes('CodeEditor') || id.includes('DiffViewer')) return 'code-editor';
          if (id.includes('StreamPanel') || id.includes('AgentLog')) return 'streaming-components';
          if (id.includes('ControlPanel') || id.includes('ConfigurationStatus')) return 'control-components';

          // Default vendor chunk for remaining node_modules
          if (id.includes('node_modules')) return 'vendor';
        },
        // Optimize chunk sizes
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `assets/[name]-[hash].js`;
        },
      },
    },
    // Bundle size limits (aggressive)
    chunkSizeWarningLimit: 200, // 200KB warning (very strict)
  },
  // Performance optimizations
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      'lucide-react',
    ],
    exclude: [
      '@monaco-editor/react', // Load Monaco dynamically
    ],
  },
}));
