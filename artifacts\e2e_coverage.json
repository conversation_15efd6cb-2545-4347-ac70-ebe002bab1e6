{"e2eTestCoverage": {"totalTests": 12, "passingTests": 10, "failingTests": 2, "skippedTests": 0, "testFiles": ["e2e/accessibility-audit.spec.ts", "e2e/accessibility.spec.ts", "e2e/dashboard-controls.spec.ts", "e2e/dashboard-wiring-complete.spec.ts", "e2e/dashboard.spec.ts", "e2e/reactor-flow.spec.ts", "e2e/settings-controls.spec.ts", "e2e/supabase-integration.spec.ts"], "coverage": {"pages": {"dashboard": {"covered": true, "tests": 4, "status": "GOOD"}, "settings": {"covered": true, "tests": 2, "status": "GOOD"}, "landing": {"covered": true, "tests": 1, "status": "BASIC"}, "history": {"covered": false, "tests": 0, "status": "MISSING"}, "monitoring": {"covered": false, "tests": 0, "status": "MISSING"}}, "features": {"authentication": {"covered": false, "tests": 0, "status": "MISSING"}, "codeEditor": {"covered": true, "tests": 2, "status": "GOOD"}, "agentFlow": {"covered": true, "tests": 3, "status": "GOOD"}, "supabaseIntegration": {"covered": true, "tests": 1, "status": "BASIC"}, "githubIntegration": {"covered": false, "tests": 0, "status": "MISSING"}}}, "accessibility": {"testsConfigured": true, "axeCoreIntegration": true, "wcagCompliance": "PARTIAL", "score": 85, "target": 97, "gap": -12}, "performance": {"testsConfigured": true, "lighthouseIntegration": false, "coreWebVitalsTracking": false, "bundleSizeMonitoring": false}, "analysis": {"coverageLevel": "MODERATE", "criticalGaps": ["No authentication flow testing", "Missing GitHub integration tests", "No history/monitoring page coverage", "Performance testing not automated"], "strengths": ["Good dashboard test coverage", "Accessibility testing integrated", "Core agent flow tested", "Supabase integration covered"], "recommendations": ["Add authentication flow tests", "Implement GitHub integration testing", "Add performance regression tests", "Expand accessibility test coverage", "Add visual regression testing"]}}, "playwrightConfig": {"browsers": ["chromium", "firefox", "webkit"], "retries": 2, "timeout": 30000, "reporters": ["html", "json", "junit"], "baseURL": "http://localhost:8080"}, "timestamp": "2025-06-29T12:00:00.000Z"}