import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from './button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { Alert, AlertDescription } from './alert';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'component' | 'widget';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorId = this.state.errorId;
    
    // Log error details
    console.error('Error Boundary caught an error:', {
      error,
      errorInfo,
      errorId,
      level: this.props.level || 'component',
      timestamp: new Date().toISOString(),
    });

    this.setState({
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorId } = this.state;
      const level = this.props.level || 'component';
      
      if (level === 'widget') {
        return (
          <div className="p-4 border border-red-200 rounded-lg bg-red-50 dark:bg-red-900/20">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-red-500" />
              <span className="text-sm font-medium text-red-700 dark:text-red-300">
                Widget Error
              </span>
            </div>
            <p className="text-xs text-red-600 dark:text-red-400 mb-2">
              This component encountered an error and couldn't render.
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={this.handleRetry}
              className="h-6 px-2 text-xs"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Retry
            </Button>
          </div>
        );
      }

      if (level === 'page') {
        return (
          <div className="min-h-screen bg-background flex items-center justify-center p-4">
            <Card className="w-full max-w-2xl">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-8 h-8 text-red-500" />
                  <div>
                    <CardTitle className="text-xl">Something went wrong</CardTitle>
                    <CardDescription>
                      The page encountered an unexpected error and couldn't load properly.
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <Alert className="border-red-500/30 bg-red-900/10">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <AlertDescription className="text-red-200">
                    <strong>Error ID:</strong> {errorId}
                    <br />
                    <strong>Message:</strong> {error?.message || 'Unknown error'}
                  </AlertDescription>
                </Alert>
                
                <div className="flex space-x-3">
                  <Button onClick={this.handleRetry} className="flex-1">
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again
                  </Button>
                  <Button variant="outline" onClick={this.handleGoHome} className="flex-1">
                    <Home className="w-4 h-4 mr-2" />
                    Go Home
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      }

      // Component level error
      return (
        <div className="p-6 border border-red-200 rounded-lg bg-red-50 dark:bg-red-900/20">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle className="w-6 h-6 text-red-500" />
            <div>
              <h3 className="text-lg font-semibold text-red-700 dark:text-red-300">
                Component Error
              </h3>
              <p className="text-sm text-red-600 dark:text-red-400">
                This section encountered an error and couldn't render properly.
              </p>
            </div>
          </div>
          
          <Alert className="mb-4 border-red-500/30 bg-red-900/10">
            <AlertDescription className="text-red-200">
              <strong>Error ID:</strong> {errorId}
              <br />
              <strong>Message:</strong> {error?.message || 'Unknown error'}
            </AlertDescription>
          </Alert>
          
          <Button onClick={this.handleRetry} size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry Component
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Specialized error boundaries
export const PageErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="page">
    {children}
  </ErrorBoundary>
);

export const ComponentErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="component">
    {children}
  </ErrorBoundary>
);

export const WidgetErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="widget">
    {children}
  </ErrorBoundary>
);

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

export default ErrorBoundary;
