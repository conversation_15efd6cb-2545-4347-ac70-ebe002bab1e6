import { PlanAgent } from '../planAgent.js';
import { PlanRequest, JSONPatch } from '../types.js';
import { createMockProviderConfig } from './setup.js';

// Import providers to trigger registration
import '../providers/OpenAIProvider.js';
import '../providers/AnthropicProvider.js';
import '../providers/VertexAIProvider.js';

describe('PlanAgent', () => {
  let planAgent: PlanAgent;

  beforeEach(() => {
    planAgent = new PlanAgent({
      provider: createMockProviderConfig('openai')
    });
  });

  describe('generatePatch', () => {
    it('should generate a valid patch for a simple request', async () => {
      const request: PlanRequest = {
        prompt: 'Add error handling to this function',
        context: { language: 'javascript' }
      };

      const patch = await planAgent.generatePatch(request);

      expect(patch).toBeDefined();
      expect(patch.operations).toBeInstanceOf(Array);
      expect(patch.operations.length).toBeGreaterThan(0);
      expect(patch.description).toBeTruthy();
      expect(patch.confidence).toBeGreaterThanOrEqual(0);
      expect(patch.confidence).toBeLessThanOrEqual(1);
    });

    it('should include previous attempts in planning context', async () => {
      const previousPatch: JSONPatch = {
        operations: [{ op: 'add', path: '/test', value: 'test' }],
        description: 'Previous attempt',
        confidence: 0.5
      };

      const request: PlanRequest = {
        prompt: 'Improve error handling',
        previousAttempts: [previousPatch]
      };

      const patch = await planAgent.generatePatch(request);

      expect(patch).toBeDefined();
      expect(patch.description).toContain('Improve error handling');
    });

    it('should handle empty prompts gracefully', async () => {
      const request: PlanRequest = {
        prompt: ''
      };

      await expect(planAgent.generatePatch(request)).rejects.toThrow();
    });

    it('should generate patches with valid operations', async () => {
      const request: PlanRequest = {
        prompt: 'Optimize performance'
      };

      const patch = await planAgent.generatePatch(request);

      patch.operations.forEach(op => {
        expect(op.op).toMatch(/^(add|remove|replace|move|copy|test)$/);
        expect(op.path).toBeTruthy();
        expect(op.path).toMatch(/^\//);
      });
    });

    it('should set appropriate confidence levels', async () => {
      const request: PlanRequest = {
        prompt: 'Simple variable rename'
      };

      const patch = await planAgent.generatePatch(request);

      expect(patch.confidence).toBeGreaterThan(0.5);
      expect(patch.confidence).toBeLessThanOrEqual(1);
    });
  });

  describe('validatePatch', () => {
    it('should validate a correct patch', async () => {
      const validPatch: JSONPatch = {
        operations: [
          { op: 'add', path: '/newProperty', value: 'test' },
          { op: 'replace', path: '/existingProperty', value: 'updated' }
        ],
        description: 'Valid patch',
        confidence: 0.8
      };

      const isValid = await planAgent.validatePatch(validPatch);
      expect(isValid).toBe(true);
    });

    it('should reject patch with invalid operations', async () => {
      const invalidPatch: JSONPatch = {
        operations: [
          { op: 'invalid', path: '/test', value: 'test' } as any
        ],
        description: 'Invalid patch',
        confidence: 0.8
      };

      const isValid = await planAgent.validatePatch(invalidPatch);
      expect(isValid).toBe(false);
    });

    it('should reject patch without operations', async () => {
      const invalidPatch = {
        description: 'No operations',
        confidence: 0.8
      } as JSONPatch;

      const isValid = await planAgent.validatePatch(invalidPatch);
      expect(isValid).toBe(false);
    });

    it('should reject patch with invalid confidence', async () => {
      const invalidPatch: JSONPatch = {
        operations: [{ op: 'add', path: '/test', value: 'test' }],
        description: 'Invalid confidence',
        confidence: 1.5
      };

      const isValid = await planAgent.validatePatch(invalidPatch);
      expect(isValid).toBe(false);
    });

    it('should reject patch without description', async () => {
      const invalidPatch = {
        operations: [{ op: 'add', path: '/test', value: 'test' }],
        confidence: 0.8
      } as JSONPatch;

      const isValid = await planAgent.validatePatch(invalidPatch);
      expect(isValid).toBe(false);
    });
  });

  describe('buildPlanningPrompt', () => {
    it('should include context in prompt', () => {
      const request: PlanRequest = {
        prompt: 'Test prompt',
        context: { language: 'typescript', framework: 'react' }
      };

      // Access private method for testing
      const prompt = (planAgent as any).buildPlanningPrompt(request);

      expect(prompt).toContain('Test prompt');
      expect(prompt).toContain('typescript');
      expect(prompt).toContain('react');
    });

    it('should include previous attempts', () => {
      const previousPatch: JSONPatch = {
        operations: [],
        description: 'Previous attempt description',
        confidence: 0.6
      };

      const request: PlanRequest = {
        prompt: 'Test prompt',
        previousAttempts: [previousPatch]
      };

      const prompt = (planAgent as any).buildPlanningPrompt(request);

      expect(prompt).toContain('Previous attempts');
      expect(prompt).toContain('Previous attempt description');
    });
  });
});
