import { useState, useEffect, useCallback, useRef } from 'react';
import { api } from '@/lib/api/services';
import { websocketService, WebSocketState } from '@/lib/api/websocketService';
import { Error<PERSON><PERSON><PERSON>, ErrorCategory } from '@/lib/errors/errorHandler';
import { useToast } from '@/hooks/use-toast';

interface ApiState<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  showToast?: boolean;
  retries?: number;
}

// Generic API hook
export function useApi<T = any>(
  apiCall: () => Promise<any>,
  dependencies: any[] = [],
  options: UseApiOptions = {}
) {
  const {
    immediate = true,
    onSuccess,
    onError,
    showToast = false,
    retries = 0
  } = options;

  const { toast } = useToast();
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const abortControllerRef = useRef<AbortController>();
  const retryCountRef = useRef(0);

  const execute = useCallback(async () => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await apiCall();
      const data = response.data || response;

      setState({
        data,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });

      retryCountRef.current = 0;
      onSuccess?.(data);

      if (showToast) {
        toast({
          title: "Success",
          description: "Operation completed successfully",
        });
      }

      return data;
    } catch (error: any) {
      const errorMessage = error.message || 'An error occurred';
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));

      // Retry logic
      if (retryCountRef.current < retries && error.retryable !== false) {
        retryCountRef.current++;
        const delay = Math.min(1000 * Math.pow(2, retryCountRef.current - 1), 10000);
        
        setTimeout(() => {
          execute();
        }, delay);
        return;
      }

      onError?.(errorMessage);

      if (showToast) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }

      throw error;
    }
  }, [apiCall, onSuccess, onError, showToast, retries, toast]);

  const refresh = useCallback(() => {
    return execute();
  }, [execute]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
    retryCountRef.current = 0;
  }, []);

  useEffect(() => {
    if (immediate) {
      execute();
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, dependencies);

  return {
    ...state,
    execute,
    refresh,
    reset,
    isStale: state.lastUpdated ? Date.now() - state.lastUpdated.getTime() > 300000 : true, // 5 minutes
  };
}

// Specific API hooks
export function useTransformations(options?: UseApiOptions) {
  return useApi(
    () => api.transformations.getUserTransformations(),
    [],
    options
  );
}

export function useTransformation(id: string | null, options?: UseApiOptions) {
  return useApi(
    () => id ? api.transformations.getTransformation(id) : Promise.resolve(null),
    [id],
    { immediate: !!id, ...options }
  );
}

export function useSystemHealth(options?: UseApiOptions) {
  return useApi(
    () => api.systemHealth.getSystemHealth(),
    [],
    options
  );
}

export function useUserProfile(options?: UseApiOptions) {
  return useApi(
    () => api.user.getCurrentUser(),
    [],
    options
  );
}

export function useNotifications(options?: UseApiOptions) {
  return useApi(
    () => api.notifications.getNotifications(),
    [],
    options
  );
}

export function useGitHubRepositories(options?: UseApiOptions) {
  return useApi(
    () => api.github.getRepositories(),
    [],
    options
  );
}

export function useDashboardStats(timeframe: string = '7d', options?: UseApiOptions) {
  return useApi(
    () => api.analytics.getDashboardStats(timeframe),
    [timeframe],
    options
  );
}

// WebSocket hook
export function useWebSocket() {
  const [state, setState] = useState<WebSocketState>(websocketService.getState());
  const [queuedMessages, setQueuedMessages] = useState(0);

  useEffect(() => {
    const unsubscribeState = websocketService.onStateChange((newState) => {
      setState(newState);
    });

    const updateQueueCount = () => {
      setQueuedMessages(websocketService.getQueuedMessageCount());
    };

    const interval = setInterval(updateQueueCount, 1000);
    updateQueueCount();

    return () => {
      unsubscribeState();
      clearInterval(interval);
    };
  }, []);

  const send = useCallback((type: string, data: any) => {
    return websocketService.send(type, data);
  }, []);

  const subscribe = useCallback((eventType: string, handler: (message: any) => void) => {
    return websocketService.on(eventType, handler);
  }, []);

  const connect = useCallback(() => {
    return websocketService.connect();
  }, []);

  const disconnect = useCallback(() => {
    websocketService.disconnect();
  }, []);

  return {
    state,
    isConnected: state === WebSocketState.CONNECTED,
    queuedMessages,
    send,
    subscribe,
    connect,
    disconnect,
  };
}

// Real-time data hook
export function useRealTimeData<T>(
  eventType: string,
  initialData: T | null = null
) {
  const [data, setData] = useState<T | null>(initialData);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    const unsubscribe = websocketService.on(eventType, (message) => {
      setData(message.data);
      setLastUpdate(new Date());
    });

    return unsubscribe;
  }, [eventType]);

  return {
    data,
    lastUpdate,
    isStale: lastUpdate ? Date.now() - lastUpdate.getTime() > 60000 : true, // 1 minute
  };
}

// Mutation hook for API operations that modify data
export function useMutation<TData = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<any>,
  options: {
    onSuccess?: (data: TData, variables: TVariables) => void;
    onError?: (error: any, variables: TVariables) => void;
    showToast?: boolean;
  } = {}
) {
  const { toast } = useToast();
  const [state, setState] = useState({
    loading: false,
    error: null as string | null,
  });

  const mutate = useCallback(async (variables: TVariables) => {
    setState({ loading: true, error: null });

    try {
      const response = await mutationFn(variables);
      const data = response.data || response;

      setState({ loading: false, error: null });
      
      options.onSuccess?.(data, variables);

      if (options.showToast) {
        toast({
          title: "Success",
          description: "Operation completed successfully",
        });
      }

      return data;
    } catch (error: any) {
      const errorMessage = error.message || 'An error occurred';
      
      setState({ loading: false, error: errorMessage });
      
      options.onError?.(error, variables);

      if (options.showToast) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }

      throw error;
    }
  }, [mutationFn, options, toast]);

  const reset = useCallback(() => {
    setState({ loading: false, error: null });
  }, []);

  return {
    ...state,
    mutate,
    reset,
  };
}

// Health check hook
export function useApiHealth() {
  const [isHealthy, setIsHealthy] = useState(true);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  useEffect(() => {
    const checkHealth = async () => {
      try {
        await api.healthCheck();
        setIsHealthy(true);
      } catch (error) {
        setIsHealthy(false);
      } finally {
        setLastCheck(new Date());
      }
    };

    // Initial check
    checkHealth();

    // Check every 30 seconds
    const interval = setInterval(checkHealth, 30000);

    return () => clearInterval(interval);
  }, []);

  return {
    isHealthy,
    lastCheck,
  };
}
