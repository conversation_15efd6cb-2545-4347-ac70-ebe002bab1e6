import React, { Suspense } from 'react'
import { Loading<PERSON>pin<PERSON>, CardSkeleton } from '@/components/ui/loading-states'

// Lazy load heavy components
export const LazyDashboard = React.lazy(() => import('@/pages/Dashboard'))
export const LazyMonitoringDashboard = React.lazy(() => import('@/pages/MonitoringDashboard'))
export const LazySettings = React.lazy(() => import('@/pages/Settings'))
export const LazyHistory = React.lazy(() => import('@/pages/History'))

// Lazy load Monaco Editor (heavy dependency)
export const LazyCodeEditor = React.lazy(() => 
  import('@/components/CodeEditor').then(module => ({
    default: module.CodeEditor
  }))
)

// Lazy load chart components with Recharts
export const LazyChartComponents = React.lazy(() =>
  import('@/components/charts/index').catch(() => ({
    default: () => (
      <div className="flex items-center justify-center p-8 text-muted-foreground">
        Charts not available
      </div>
    )
  }))
)

// Lazy load Recharts library specifically
export const LazyRecharts = React.lazy(() =>
  import('recharts').then(module => ({
    default: module
  }))
)

// Lazy load Monaco Editor with worker optimization
export const LazyMonacoEditor = React.lazy(() =>
  import('@monaco-editor/react').then(module => {
    // Configure Monaco workers for better performance
    if (typeof window !== 'undefined') {
      window.MonacoEnvironment = {
        getWorkerUrl: function (moduleId, label) {
          if (label === 'json') {
            return '/monaco-editor/min/vs/language/json/json.worker.js'
          }
          if (label === 'css' || label === 'scss' || label === 'less') {
            return '/monaco-editor/min/vs/language/css/css.worker.js'
          }
          if (label === 'html' || label === 'handlebars' || label === 'razor') {
            return '/monaco-editor/min/vs/language/html/html.worker.js'
          }
          if (label === 'typescript' || label === 'javascript') {
            return '/monaco-editor/min/vs/language/typescript/ts.worker.js'
          }
          return '/monaco-editor/min/vs/editor/editor.worker.js'
        }
      }
    }
    return { default: module.default }
  })
)

// Lazy load advanced features
export const LazyAdvancedFeatures = React.lazy(() =>
  import('@/components/advanced/index').catch(() => ({
    default: () => (
      <div className="flex items-center justify-center p-8 text-muted-foreground">
        Advanced features not available
      </div>
    )
  }))
)

// HOC for lazy loading with custom fallback
interface LazyWrapperProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  errorFallback?: React.ReactNode
  className?: string
}

export function LazyWrapper({
  children,
  fallback = <LoadingSpinner message="Loading component..." />,
  errorFallback = <div>Failed to load component</div>,
  className
}: LazyWrapperProps) {
  return (
    <div className={`lazy-component-container ${className || ''}`}>
      <Suspense fallback={fallback}>
        <ErrorBoundary fallback={errorFallback}>
          {children}
        </ErrorBoundary>
      </Suspense>
    </div>
  )
}

// Error boundary for lazy components
interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback: React.ReactNode
}

interface ErrorBoundaryState {
  hasError: boolean
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(): ErrorBoundaryState {
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }

    return this.props.children
  }
}

// Preload utility for critical components
export function preloadComponent(componentImport: () => Promise<any>) {
  const componentPromise = componentImport()
  return componentPromise
}

// Preload critical components on idle
export function preloadCriticalComponents() {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      preloadComponent(() => import('@/components/CodeEditor'))
      preloadComponent(() => import('@/pages/Dashboard'))
    })
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      preloadComponent(() => import('@/components/CodeEditor'))
      preloadComponent(() => import('@/pages/Dashboard'))
    }, 1000)
  }
}

// Component-specific lazy loading with skeletons
export function LazyCodeEditorWithSkeleton(props: any) {
  return (
    <LazyWrapper 
      fallback={
        <div className="h-full border border-border rounded-lg p-4">
          <div className="space-y-4">
            <div className="h-4 bg-muted rounded animate-pulse" />
            <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
            <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
            <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
          </div>
        </div>
      }
    >
      <LazyCodeEditor {...props} />
    </LazyWrapper>
  )
}

export function LazyDashboardWithSkeleton() {
  return (
    <LazyWrapper
      fallback={
        <div className="min-h-screen bg-background">
          {/* Header skeleton */}
          <div className="border-b border-border p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="h-8 w-20 bg-muted rounded animate-pulse" />
                <div className="h-8 w-48 bg-muted rounded animate-pulse" />
              </div>
              <div className="flex space-x-2">
                <div className="h-8 w-24 bg-muted rounded animate-pulse" />
                <div className="h-8 w-24 bg-muted rounded animate-pulse" />
              </div>
            </div>
          </div>

          {/* Main content skeleton */}
          <div className="flex h-[calc(100vh-73px)]">
            <div className="flex-1 p-6">
              <CardSkeleton showImage lines={5} />
            </div>
            <div className="w-80 border-l border-border p-6">
              <CardSkeleton lines={8} />
            </div>
          </div>
        </div>
      }
    >
      <LazyDashboard />
    </LazyWrapper>
  )
}

export function LazyMonitoringDashboardWithSkeleton() {
  return (
    <LazyWrapper
      fallback={
        <div className="min-h-screen bg-background p-6">
          {/* Dashboard header skeleton */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="h-8 w-48 bg-muted rounded animate-pulse mb-2" />
                <div className="h-4 w-96 bg-muted rounded animate-pulse" />
              </div>
            </div>

            {/* Tab navigation skeleton */}
            <div className="flex space-x-4">
              <div className="h-10 w-24 bg-muted rounded animate-pulse" />
              <div className="h-10 w-24 bg-muted rounded animate-pulse" />
              <div className="h-10 w-24 bg-muted rounded animate-pulse" />
              <div className="h-10 w-24 bg-muted rounded animate-pulse" />
            </div>

            {/* Widget grid skeleton */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              <CardSkeleton lines={6} />
              <CardSkeleton lines={6} />
              <CardSkeleton lines={6} />
              <CardSkeleton lines={8} />
              <div className="lg:col-span-2">
                <CardSkeleton lines={10} />
              </div>
            </div>
          </div>
        </div>
      }
    >
      <LazyMonitoringDashboard />
    </LazyWrapper>
  )
}

// Dynamic import utility with retry
export async function dynamicImport<T>(
  importFn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000
): Promise<T> {
  try {
    return await importFn()
  } catch (error) {
    if (retries > 0) {
      await new Promise(resolve => setTimeout(resolve, delay))
      return dynamicImport(importFn, retries - 1, delay * 2)
    }
    throw error
  }
}

// Route-based code splitting helper
export function createLazyRoute(
  importFn: () => Promise<{ default: React.ComponentType<any> }>,
  fallback?: React.ReactNode
) {
  const LazyComponent = React.lazy(importFn)
  
  return function LazyRoute(props: any) {
    return (
      <Suspense fallback={fallback || <LoadingSpinner />}>
        <LazyComponent {...props} />
      </Suspense>
    )
  }
}

// Bundle analyzer helper (development only)
export function analyzeBundleSize() {
  if (process.env.NODE_ENV === 'development') {
    // For Vite, we would use rollup-plugin-visualizer instead
    console.log('Bundle analysis available via rollup-plugin-visualizer')
    console.log('Run: npm run build -- --analyze to generate bundle analysis')
  }
}

// Performance monitoring for lazy components
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) {
  return React.forwardRef<any, P>((props, ref) => {
    React.useEffect(() => {
      const startTime = performance.now()
      
      return () => {
        const endTime = performance.now()
        const renderTime = endTime - startTime
        
        if (renderTime > 100) { // Log slow renders
          console.warn(`Slow render detected for ${componentName}: ${renderTime.toFixed(2)}ms`)
        }
        
        // Send to analytics if available
        if ('gtag' in window) {
          (window as any).gtag('event', 'component_render_time', {
            component_name: componentName,
            render_time: renderTime
          })
        }
      }
    })

    return <Component ref={ref} {...props} />
  })
}

// Resource hints for better loading
export function addResourceHints() {
  // Preload critical fonts
  const fontLink = document.createElement('link')
  fontLink.rel = 'preload'
  fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
  fontLink.as = 'style'
  document.head.appendChild(fontLink)

  // Prefetch likely next pages
  const prefetchDashboard = document.createElement('link')
  prefetchDashboard.rel = 'prefetch'
  prefetchDashboard.href = '/dashboard'
  document.head.appendChild(prefetchDashboard)

  // Preconnect to external services
  const preconnectSupabase = document.createElement('link')
  preconnectSupabase.rel = 'preconnect'
  preconnectSupabase.href = 'https://supabase.co'
  document.head.appendChild(preconnectSupabase)
}

// Initialize performance optimizations
export function initializePerformanceOptimizations() {
  // Preload critical components
  preloadCriticalComponents()
  
  // Add resource hints
  addResourceHints()
  
  // Enable performance monitoring in development
  if (process.env.NODE_ENV === 'development') {
    analyzeBundleSize()
  }
}
