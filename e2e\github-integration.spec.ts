import { test, expect } from '@playwright/test';

test.describe('GitHub Integration E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Set up authenticated Supabase state
    await page.addInitScript(() => {
      const authToken = {
        access_token: 'valid-supabase-token',
        user: {
          id: 'test-user-123',
          email: '<EMAIL>'
        },
        expires_at: Date.now() / 1000 + 3600
      };
      window.localStorage.setItem('supabase.auth.token', JSON.stringify(authToken));
    });

    // Mock GitHub API responses
    await page.route('**/api.github.com/**', async route => {
      const url = route.request().url();
      
      if (url.includes('/user')) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            login: 'testuser',
            name: 'Test User',
            email: '<EMAIL>',
            avatar_url: 'https://avatars.githubusercontent.com/u/123456?v=4',
            public_repos: 42,
            followers: 10,
            following: 15
          })
        });
      } else if (url.includes('/user/repos')) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              id: 1,
              name: 'test-repo',
              full_name: 'testuser/test-repo',
              private: false,
              html_url: 'https://github.com/testuser/test-repo',
              description: 'A test repository',
              default_branch: 'main'
            },
            {
              id: 2,
              name: 'another-repo',
              full_name: 'testuser/another-repo',
              private: true,
              html_url: 'https://github.com/testuser/another-repo',
              description: 'Another test repository',
              default_branch: 'master'
            }
          ])
        });
      } else if (url.includes('/repos/') && url.includes('/pulls')) {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 123,
            number: 42,
            html_url: 'https://github.com/testuser/test-repo/pull/42',
            title: 'feat: reactor auto-optimization',
            body: 'Automated code optimization via Metamorphic Reactor',
            head: {
              ref: 'reactor/auto-optimization-123',
              sha: 'abc123def456'
            },
            base: {
              ref: 'main'
            },
            state: 'open',
            mergeable: true
          })
        });
      }
    });

    // Mock Supabase GitHub functions
    await page.route('**/functions/v1/github-oauth', async route => {
      const method = route.request().method();
      
      if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            hasToken: true,
            tokenInfo: {
              login: 'testuser',
              name: 'Test User',
              avatar_url: 'https://avatars.githubusercontent.com/u/123456?v=4'
            }
          })
        });
      } else if (method === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            user: {
              login: 'testuser',
              name: 'Test User',
              avatar_url: 'https://avatars.githubusercontent.com/u/123456?v=4'
            }
          })
        });
      }
    });

    await page.route('**/functions/v1/create-pr', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          pr: {
            url: 'https://github.com/testuser/test-repo/pull/42',
            number: 42,
            branch: 'reactor/auto-optimization-123',
            title: 'feat: reactor auto-optimization'
          }
        })
      });
    });
  });

  test('should display GitHub connection status', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Should show GitHub connected status
    await expect(page.locator('[data-testid="github-status"]')).toContainText('Connected');
    await expect(page.locator('text=testuser')).toBeVisible();
  });

  test('should handle GitHub OAuth flow', async ({ page }) => {
    // Start with disconnected state
    await page.route('**/functions/v1/github-oauth', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            hasToken: false,
            tokenInfo: null
          })
        });
      }
    });

    await page.goto('/dashboard');
    
    // Should show "Connect GitHub" button
    await expect(page.locator('text=Connect GitHub')).toBeVisible();
    
    // Click connect GitHub
    await page.click('text=Connect GitHub');
    
    // Should open OAuth popup (in real scenario)
    // For testing, we'll simulate the OAuth callback
    await page.evaluate(() => {
      // Simulate OAuth success callback
      window.postMessage({
        type: 'github-oauth-success',
        data: {
          login: 'testuser',
          name: 'Test User',
          avatar_url: 'https://avatars.githubusercontent.com/u/123456?v=4'
        }
      }, '*');
    });
    
    // Should now show connected state
    await expect(page.locator('text=Connected')).toBeVisible();
    await expect(page.locator('text=testuser')).toBeVisible();
  });

  test('should list user repositories', async ({ page }) => {
    await page.goto('/settings');
    
    // Navigate to GitHub settings
    await page.click('text=GitHub Integration');
    
    // Should show repository list
    await expect(page.locator('text=test-repo')).toBeVisible();
    await expect(page.locator('text=another-repo')).toBeVisible();
    
    // Should show repository details
    await expect(page.locator('text=A test repository')).toBeVisible();
    await expect(page.locator('text=42 public repos')).toBeVisible();
  });

  test('should create pull request after reactor completion', async ({ page }) => {
    // Mock successful reactor loop
    await page.route('**/functions/v1/ai-loop', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          finalPatch: {
            operations: [
              { op: 'replace', path: '/function', value: 'optimized function' }
            ],
            description: 'Optimized function for better performance'
          },
          finalScore: 0.92,
          completed: true
        })
      });
    });

    await page.goto('/dashboard');
    
    // Run reactor loop
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('function fibonacci(n) { return n <= 1 ? n : fibonacci(n-1) + fibonacci(n-2); }');
    
    await page.click('text=Start');
    
    // Wait for completion
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 10000 });
    
    // Should show Create PR button
    await expect(page.locator('text=Create PR')).toBeVisible();
    
    // Click Create PR
    await page.click('text=Create PR');
    
    // Should show PR creation dialog
    await expect(page.locator('text=Create Pull Request')).toBeVisible();
    
    // Fill PR details
    await page.fill('input[placeholder="PR title"]', 'feat: optimize fibonacci function');
    await page.fill('textarea[placeholder="PR description"]', 'Added memoization for better performance');
    
    // Select repository
    await page.selectOption('select[name="repository"]', 'testuser/test-repo');
    
    // Create PR
    await page.click('button:has-text("Create Pull Request")');
    
    // Should show success message
    await expect(page.locator('text=Pull Request created successfully')).toBeVisible();
    await expect(page.locator('text=#42')).toBeVisible();
    
    // Should show link to PR
    const prLink = page.locator('a[href*="github.com"][href*="/pull/42"]');
    await expect(prLink).toBeVisible();
  });

  test('should handle GitHub API errors gracefully', async ({ page }) => {
    // Mock GitHub API error
    await page.route('**/functions/v1/create-pr', async route => {
      await route.fulfill({
        status: 403,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Insufficient permissions',
          message: 'Token does not have required permissions'
        })
      });
    });

    await page.goto('/dashboard');
    
    // Run reactor loop first
    await page.route('**/functions/v1/ai-loop', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          finalPatch: { operations: [] },
          completed: true
        })
      });
    });

    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.type('test code');
    await page.click('text=Start');
    
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 10000 });
    
    // Try to create PR
    await page.click('text=Create PR');
    await page.click('button:has-text("Create Pull Request")');
    
    // Should show error message
    await expect(page.locator('text=Insufficient permissions')).toBeVisible();
  });

  test('should disconnect GitHub account', async ({ page }) => {
    await page.goto('/settings');
    
    // Navigate to GitHub settings
    await page.click('text=GitHub Integration');
    
    // Should show connected state
    await expect(page.locator('text=Connected as testuser')).toBeVisible();
    
    // Click disconnect
    await page.click('text=Disconnect');
    
    // Should show confirmation dialog
    await expect(page.locator('text=Are you sure you want to disconnect')).toBeVisible();
    
    // Confirm disconnection
    await page.click('button:has-text("Disconnect")');
    
    // Mock disconnection response
    await page.route('**/functions/v1/github-oauth', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      }
    });
    
    // Should show disconnected state
    await expect(page.locator('text=Connect GitHub')).toBeVisible();
  });

  test('should validate repository permissions before PR creation', async ({ page }) => {
    // Mock repository with limited permissions
    await page.route('**/api.github.com/repos/testuser/test-repo', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 1,
          name: 'test-repo',
          full_name: 'testuser/test-repo',
          permissions: {
            admin: false,
            push: false,
            pull: true
          }
        })
      });
    });

    await page.goto('/dashboard');
    
    // Complete reactor loop
    await page.route('**/functions/v1/ai-loop', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          finalPatch: { operations: [] },
          completed: true
        })
      });
    });

    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.type('test code');
    await page.click('text=Start');
    
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 10000 });
    
    // Try to create PR
    await page.click('text=Create PR');
    
    // Should show permission warning
    await expect(page.locator('text=Insufficient repository permissions')).toBeVisible();
  });
});
