import React from 'react';
import { cn } from '@/lib/utils';
import { useSkipLinks, useFocusTrap } from '@/hooks/useFocusManagement';

// Skip link interface
export interface SkipLink {
  id: string;
  label: string;
  target: string;
  description?: string;
}

// Skip links component
export interface SkipLinksProps {
  links?: SkipLink[];
  className?: string;
}

// Default skip links for the application
const defaultSkipLinks: SkipLink[] = [
  {
    id: 'skip-to-main',
    label: 'Skip to main content',
    target: '#main-content',
    description: 'Jump to the main content area',
  },
  {
    id: 'skip-to-nav',
    label: 'Skip to navigation',
    target: '#main-navigation',
    description: 'Jump to the main navigation menu',
  },
  {
    id: 'skip-to-search',
    label: 'Skip to search',
    target: '#search-input',
    description: 'Jump to the search input field',
  },
  {
    id: 'skip-to-footer',
    label: 'Skip to footer',
    target: '#footer',
    description: 'Jump to the footer section',
  },
];

export const SkipLinks: React.FC<SkipLinksProps> = ({
  links = defaultSkipLinks,
  className,
}) => {
  const skipLinksRef = useSkipLinks();

  const handleSkipLinkClick = (target: string) => {
    const element = document.querySelector(target);
    if (element) {
      // Make the target focusable if it isn't already
      const targetElement = element as HTMLElement;
      if (targetElement.tabIndex === -1) {
        targetElement.tabIndex = -1;
      }
      
      // Focus the target element
      targetElement.focus();
      
      // Scroll to the element
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  return (
    <nav
      ref={skipLinksRef}
      className={cn(
        "fixed top-0 left-0 right-0 z-[9999] bg-primary text-primary-foreground shadow-lg transform -translate-y-full transition-transform duration-200 focus-within:translate-y-0",
        className
      )}
      aria-label="Skip navigation links"
    >
      <div className="container mx-auto px-4 py-2">
        <ul className="flex flex-wrap gap-4">
          {links.map((link) => (
            <li key={link.id}>
              <a
                href={link.target}
                onClick={(e) => {
                  e.preventDefault();
                  handleSkipLinkClick(link.target);
                }}
                className="inline-block px-3 py-2 bg-primary-foreground text-primary rounded focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-accent hover:text-accent-foreground transition-colors"
                title={link.description}
              >
                {link.label}
              </a>
            </li>
          ))}
        </ul>
      </div>
    </nav>
  );
};

// Focus management wrapper component
export interface FocusManagerProps {
  children: React.ReactNode;
  skipLinks?: SkipLink[];
  enableFocusOutline?: boolean;
  className?: string;
}

export const FocusManager: React.FC<FocusManagerProps> = ({
  children,
  skipLinks,
  enableFocusOutline = true,
  className,
}) => {
  React.useEffect(() => {
    // Add focus-visible polyfill behavior
    let hadKeyboardEvent = false;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.metaKey || e.altKey || e.ctrlKey) return;
      hadKeyboardEvent = true;
    };

    const handlePointerDown = () => {
      hadKeyboardEvent = false;
    };

    const handleFocus = (e: FocusEvent) => {
      const target = e.target as HTMLElement;
      if (hadKeyboardEvent || target.matches(':focus-visible')) {
        target.classList.add('focus-visible');
      } else {
        target.classList.remove('focus-visible');
      }
    };

    const handleBlur = (e: FocusEvent) => {
      const target = e.target as HTMLElement;
      target.classList.remove('focus-visible');
    };

    if (enableFocusOutline) {
      document.addEventListener('keydown', handleKeyDown, true);
      document.addEventListener('pointerdown', handlePointerDown, true);
      document.addEventListener('focus', handleFocus, true);
      document.addEventListener('blur', handleBlur, true);
    }

    return () => {
      if (enableFocusOutline) {
        document.removeEventListener('keydown', handleKeyDown, true);
        document.removeEventListener('pointerdown', handlePointerDown, true);
        document.removeEventListener('focus', handleFocus, true);
        document.removeEventListener('blur', handleBlur, true);
      }
    };
  }, [enableFocusOutline]);

  return (
    <div className={cn("focus-manager", className)}>
      <SkipLinks links={skipLinks} />
      {children}
    </div>
  );
};

// Landmark component for semantic navigation
export interface LandmarkProps {
  as?: 'main' | 'nav' | 'aside' | 'section' | 'header' | 'footer';
  id?: string;
  label?: string;
  children: React.ReactNode;
  className?: string;
}

export const Landmark: React.FC<LandmarkProps> = ({
  as: Component = 'div',
  id,
  label,
  children,
  className,
}) => {
  return (
    <Component
      id={id}
      aria-label={label}
      className={cn("focus:outline-none", className)}
      tabIndex={-1}
    >
      {children}
    </Component>
  );
};

// Accessible button component with enhanced keyboard support
export interface AccessibleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

export const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  children,
  className,
  onKeyDown,
  ...props
}) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    // Handle Enter and Space keys
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      e.currentTarget.click();
    }
    
    onKeyDown?.(e);
  };

  const baseClasses = "inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none";
  
  const variantClasses = {
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    outline: "border border-input hover:bg-accent hover:text-accent-foreground",
  };
  
  const sizeClasses = {
    sm: "h-8 px-3 text-sm",
    md: "h-10 px-4",
    lg: "h-12 px-6 text-lg",
  };

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      onKeyDown={handleKeyDown}
      aria-disabled={loading}
      {...props}
    >
      {loading ? (
        <>
          <span className="sr-only">Loading...</span>
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
        </>
      ) : null}
      {children}
    </button>
  );
};

// Screen reader only text component
export const ScreenReaderOnly: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <span className="sr-only">
      {children}
    </span>
  );
};

// Live region component for dynamic content announcements
export interface LiveRegionProps {
  children: React.ReactNode;
  politeness?: 'polite' | 'assertive' | 'off';
  atomic?: boolean;
  relevant?: 'additions' | 'removals' | 'text' | 'all';
  className?: string;
}

export const LiveRegion: React.FC<LiveRegionProps> = ({
  children,
  politeness = 'polite',
  atomic = false,
  relevant = 'additions text',
  className,
}) => {
  return (
    <div
      aria-live={politeness}
      aria-atomic={atomic}
      aria-relevant={relevant}
      className={cn("sr-only", className)}
    >
      {children}
    </div>
  );
};

// Focus trap component for modals and dialogs
export interface FocusTrapProps {
  children: React.ReactNode;
  active?: boolean;
  className?: string;
}

export const FocusTrap: React.FC<FocusTrapProps> = ({
  children,
  active = true,
  className,
}) => {
  const containerRef = useFocusTrap(active);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
};

export default {
  SkipLinks,
  FocusManager,
  Landmark,
  AccessibleButton,
  ScreenReaderOnly,
  LiveRegion,
  FocusTrap,
};
