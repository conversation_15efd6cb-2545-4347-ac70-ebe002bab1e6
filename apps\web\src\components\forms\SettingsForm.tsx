import React from 'react';
import { ValidatedForm, FormField } from '@/components/ui/validated-form';
import { VALIDATION_RULES, combineRules } from '@/lib/validation/schemas';
import { useToast } from '@/hooks/use-toast';
import { useSettingsActions, useSettingsSelector } from '@/stores/StoreProvider';

// Settings form data type
interface SettingsFormData {
  // AI Provider Settings
  plannerModel: string;
  criticModel: string;
  temperature: number;
  maxTokens: number;
  
  // Transformation Settings
  defaultMaxIterations: number;
  defaultScoreThreshold: number;
  autoCreatePR: boolean;
  
  // Cost Guard Settings
  maxCostPerLoop: number;
  maxCostPerHour: number;
  maxCostPerDay: number;
  
  // Notification Settings
  emailNotifications: boolean;
  browserNotifications: boolean;
  soundNotifications: boolean;
  
  // UI Settings
  compactMode: boolean;
  showAdvancedOptions: boolean;
  autoRefreshInterval: number;
  
  // GitHub Settings
  githubRepo: string;
  githubBranch: string;
}

export const SettingsForm: React.FC = () => {
  const { toast } = useToast();
  
  // Get current settings from store
  const settings = useSettingsSelector((state) => ({
    plannerProvider: state.plannerProvider,
    criticProvider: state.criticProvider,
    defaultMaxIterations: state.defaultMaxIterations,
    defaultScoreThreshold: state.defaultScoreThreshold,
    autoCreatePR: state.autoCreatePR,
    costGuard: state.costGuard,
    notifications: state.notifications,
    ui: state.ui,
    githubRepo: state.githubRepo,
  }));

  // Settings actions
  const {
    updatePlannerProvider,
    updateCriticProvider,
    setDefaultMaxIterations,
    setDefaultScoreThreshold,
    setAutoCreatePR,
    updateCostGuard,
    updateNotificationSettings,
    updateUISettings,
    setGitHubRepo,
  } = useSettingsActions();

  // Initial form values
  const initialValues: SettingsFormData = {
    plannerModel: settings.plannerProvider.model,
    criticModel: settings.criticProvider.model,
    temperature: settings.plannerProvider.temperature,
    maxTokens: settings.plannerProvider.maxTokens,
    defaultMaxIterations: settings.defaultMaxIterations,
    defaultScoreThreshold: settings.defaultScoreThreshold,
    autoCreatePR: settings.autoCreatePR,
    maxCostPerLoop: settings.costGuard.maxCostPerLoop,
    maxCostPerHour: settings.costGuard.maxCostPerHour,
    maxCostPerDay: settings.costGuard.maxCostPerDay,
    emailNotifications: settings.notifications.email.enabled,
    browserNotifications: settings.notifications.browser.enabled,
    soundNotifications: settings.notifications.sound.enabled,
    compactMode: settings.ui.compactMode,
    showAdvancedOptions: settings.ui.showAdvancedOptions,
    autoRefreshInterval: settings.ui.autoRefreshInterval,
    githubRepo: settings.githubRepo?.name || '',
    githubBranch: settings.githubRepo?.branch || 'main',
  };

  // Form fields configuration
  const formFields: FormField[] = [
    // AI Provider Settings Section
    {
      name: 'plannerModel',
      type: 'select',
      label: 'Planner Model',
      description: 'AI model used for planning transformations',
      required: true,
      options: [
        { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
        { value: 'gpt-4', label: 'GPT-4' },
        { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
        { value: 'claude-3-opus', label: 'Claude 3 Opus' },
        { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' },
        { value: 'gemini-pro', label: 'Gemini Pro' },
      ],
      rules: VALIDATION_RULES.required(),
    },
    {
      name: 'criticModel',
      type: 'select',
      label: 'Critic Model',
      description: 'AI model used for critiquing transformations',
      required: true,
      options: [
        { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' },
        { value: 'claude-3-opus', label: 'Claude 3 Opus' },
        { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
        { value: 'gpt-4', label: 'GPT-4' },
        { value: 'gemini-pro', label: 'Gemini Pro' },
      ],
      rules: VALIDATION_RULES.required(),
    },
    {
      name: 'temperature',
      type: 'number',
      label: 'Temperature',
      description: 'Controls randomness in AI responses (0.0 - 1.0)',
      required: true,
      rules: combineRules(
        VALIDATION_RULES.required(),
        VALIDATION_RULES.number(),
        VALIDATION_RULES.range(0, 1)
      ),
    },
    {
      name: 'maxTokens',
      type: 'number',
      label: 'Max Tokens',
      description: 'Maximum tokens per AI request',
      required: true,
      rules: combineRules(
        VALIDATION_RULES.required(),
        VALIDATION_RULES.number(),
        VALIDATION_RULES.range(100, 8000)
      ),
    },

    // Transformation Settings
    {
      name: 'defaultMaxIterations',
      type: 'number',
      label: 'Default Max Iterations',
      description: 'Default maximum iterations for transformations',
      required: true,
      rules: combineRules(
        VALIDATION_RULES.required(),
        VALIDATION_RULES.number(),
        VALIDATION_RULES.range(1, 50)
      ),
    },
    {
      name: 'defaultScoreThreshold',
      type: 'number',
      label: 'Default Score Threshold',
      description: 'Default score threshold for transformation completion (0.0 - 1.0)',
      required: true,
      rules: combineRules(
        VALIDATION_RULES.required(),
        VALIDATION_RULES.number(),
        VALIDATION_RULES.range(0.1, 1.0)
      ),
    },
    {
      name: 'autoCreatePR',
      type: 'checkbox',
      label: 'Auto-create Pull Requests',
      description: 'Automatically create pull requests for successful transformations',
    },

    // Cost Guard Settings
    {
      name: 'maxCostPerLoop',
      type: 'number',
      label: 'Max Cost Per Loop ($)',
      description: 'Maximum cost allowed per transformation loop',
      required: true,
      rules: combineRules(
        VALIDATION_RULES.required(),
        VALIDATION_RULES.number(),
        VALIDATION_RULES.min(0.01)
      ),
    },
    {
      name: 'maxCostPerHour',
      type: 'number',
      label: 'Max Cost Per Hour ($)',
      description: 'Maximum cost allowed per hour',
      required: true,
      rules: combineRules(
        VALIDATION_RULES.required(),
        VALIDATION_RULES.number(),
        VALIDATION_RULES.min(0.01)
      ),
    },
    {
      name: 'maxCostPerDay',
      type: 'number',
      label: 'Max Cost Per Day ($)',
      description: 'Maximum cost allowed per day',
      required: true,
      rules: combineRules(
        VALIDATION_RULES.required(),
        VALIDATION_RULES.number(),
        VALIDATION_RULES.min(0.01)
      ),
    },

    // Notification Settings
    {
      name: 'emailNotifications',
      type: 'checkbox',
      label: 'Email Notifications',
      description: 'Receive notifications via email',
    },
    {
      name: 'browserNotifications',
      type: 'checkbox',
      label: 'Browser Notifications',
      description: 'Show browser notifications',
    },
    {
      name: 'soundNotifications',
      type: 'checkbox',
      label: 'Sound Notifications',
      description: 'Play sound for notifications',
    },

    // UI Settings
    {
      name: 'compactMode',
      type: 'checkbox',
      label: 'Compact Mode',
      description: 'Use compact UI layout',
    },
    {
      name: 'showAdvancedOptions',
      type: 'checkbox',
      label: 'Show Advanced Options',
      description: 'Display advanced configuration options',
    },
    {
      name: 'autoRefreshInterval',
      type: 'number',
      label: 'Auto Refresh Interval (seconds)',
      description: 'How often to refresh data automatically',
      required: true,
      rules: combineRules(
        VALIDATION_RULES.required(),
        VALIDATION_RULES.number(),
        VALIDATION_RULES.range(5, 300)
      ),
    },

    // GitHub Settings
    {
      name: 'githubRepo',
      type: 'text',
      label: 'GitHub Repository',
      description: 'Default GitHub repository for transformations',
      placeholder: 'owner/repository',
      rules: VALIDATION_RULES.pattern(
        /^[a-zA-Z0-9_.-]+\/[a-zA-Z0-9_.-]+$/,
        'Must be in format: owner/repository'
      ),
    },
    {
      name: 'githubBranch',
      type: 'text',
      label: 'Default Branch',
      description: 'Default branch for pull requests',
      placeholder: 'main',
      rules: combineRules(
        VALIDATION_RULES.required(),
        VALIDATION_RULES.pattern(
          /^[a-zA-Z0-9_.-]+$/,
          'Branch name can only contain letters, numbers, dots, hyphens, and underscores'
        )
      ),
    },
  ];

  // Handle form submission
  const handleSubmit = async (values: SettingsFormData) => {
    try {
      // Update AI provider settings
      updatePlannerProvider({
        model: values.plannerModel,
        temperature: values.temperature,
        maxTokens: values.maxTokens,
      });

      updateCriticProvider({
        model: values.criticModel,
      });

      // Update transformation settings
      setDefaultMaxIterations(values.defaultMaxIterations);
      setDefaultScoreThreshold(values.defaultScoreThreshold);
      setAutoCreatePR(values.autoCreatePR);

      // Update cost guard settings
      updateCostGuard({
        maxCostPerLoop: values.maxCostPerLoop,
        maxCostPerHour: values.maxCostPerHour,
        maxCostPerDay: values.maxCostPerDay,
      });

      // Update notification settings
      updateNotificationSettings({
        email: { ...settings.notifications.email, enabled: values.emailNotifications },
        browser: { ...settings.notifications.browser, enabled: values.browserNotifications },
        sound: { ...settings.notifications.sound, enabled: values.soundNotifications },
      });

      // Update UI settings
      updateUISettings({
        compactMode: values.compactMode,
        showAdvancedOptions: values.showAdvancedOptions,
        autoRefreshInterval: values.autoRefreshInterval,
      });

      // Update GitHub settings
      if (values.githubRepo) {
        const [owner, name] = values.githubRepo.split('/');
        setGitHubRepo({
          owner,
          name,
          branch: values.githubBranch,
        });
      }

      toast({
        title: 'Settings Updated',
        description: 'Your settings have been saved successfully.',
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <ValidatedForm
      fields={formFields}
      initialValues={initialValues}
      onSubmit={handleSubmit}
      title="Application Settings"
      description="Configure your AI models, cost limits, notifications, and other preferences."
      submitText="Save Settings"
      resetText="Reset to Defaults"
      showReset={true}
      className="max-w-4xl"
    />
  );
};

export default SettingsForm;
