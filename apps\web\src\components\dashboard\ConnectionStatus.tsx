import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useWebSocket } from '@/hooks/useApiService';
import { WebSocketState } from '@/lib/api/websocketService';

interface ConnectionStatusProps {
  compact?: boolean;
  showDetails?: boolean;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  compact = false,
  showDetails = true
}) => {
  const { state, isConnected, queuedMessages, connect, disconnect } = useWebSocket();

  const getStatusIcon = () => {
    switch (state) {
      case WebSocketState.CONNECTED:
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case WebSocketState.CONNECTING:
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />;
      case WebSocketState.RECONNECTING:
        return <RefreshCw className="w-4 h-4 text-yellow-600 animate-spin" />;
      case WebSocketState.ERROR:
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case WebSocketState.DISCONNECTED:
      default:
        return <WifiOff className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusBadgeVariant = () => {
    switch (state) {
      case WebSocketState.CONNECTED:
        return 'default' as const;
      case WebSocketState.CONNECTING:
      case WebSocketState.RECONNECTING:
        return 'secondary' as const;
      case WebSocketState.ERROR:
        return 'destructive' as const;
      case WebSocketState.DISCONNECTED:
      default:
        return 'outline' as const;
    }
  };

  const getStatusText = () => {
    switch (state) {
      case WebSocketState.CONNECTED:
        return 'Connected';
      case WebSocketState.CONNECTING:
        return 'Connecting...';
      case WebSocketState.RECONNECTING:
        return 'Reconnecting...';
      case WebSocketState.ERROR:
        return 'Connection Error';
      case WebSocketState.DISCONNECTED:
      default:
        return 'Disconnected';
    }
  };

  const getStatusDescription = () => {
    switch (state) {
      case WebSocketState.CONNECTED:
        return 'Real-time updates active';
      case WebSocketState.CONNECTING:
        return 'Establishing connection...';
      case WebSocketState.RECONNECTING:
        return 'Attempting to reconnect...';
      case WebSocketState.ERROR:
        return 'Unable to connect to server';
      case WebSocketState.DISCONNECTED:
      default:
        return 'Real-time updates unavailable';
    }
  };

  const handleToggleConnection = () => {
    if (isConnected) {
      disconnect();
    } else {
      connect();
    }
  };

  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        {getStatusIcon()}
        <Badge variant={getStatusBadgeVariant()}>
          {getStatusText()}
        </Badge>
        {queuedMessages > 0 && (
          <Badge variant="outline" className="text-xs">
            {queuedMessages} queued
          </Badge>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">{getStatusText()}</span>
              <Badge variant={getStatusBadgeVariant()} className="text-xs">
                Real-time
              </Badge>
            </div>
            {showDetails && (
              <div className="text-xs text-muted-foreground">
                {getStatusDescription()}
              </div>
            )}
          </div>
        </div>
        
        {queuedMessages > 0 && (
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3 text-yellow-600" />
            <span className="text-xs text-muted-foreground">
              {queuedMessages} message{queuedMessages !== 1 ? 's' : ''} queued
            </span>
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2">
        {state === WebSocketState.ERROR && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleToggleConnection}
            className="h-7 px-2 text-xs"
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            Retry
          </Button>
        )}
        
        {showDetails && (
          <Button
            size="sm"
            variant="ghost"
            onClick={handleToggleConnection}
            className="h-7 px-2 text-xs"
          >
            {isConnected ? (
              <>
                <WifiOff className="w-3 h-3 mr-1" />
                Disconnect
              </>
            ) : (
              <>
                <Wifi className="w-3 h-3 mr-1" />
                Connect
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default ConnectionStatus;
