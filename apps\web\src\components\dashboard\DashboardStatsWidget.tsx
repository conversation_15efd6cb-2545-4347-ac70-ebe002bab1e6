import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  DollarSign,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  BarChart3,
  Zap
} from 'lucide-react';
import { DashboardWidgetSkeleton } from '@/components/ui/loading-states';

interface DashboardStatsData {
  totalTransformations: number;
  completedTransformations: number;
  runningTransformations: number;
  failedTransformations: number;
  totalCost: number;
  averageScore: number;
  averageExecutionTime: number;
  successRate: number;
  recentActivity: any[];
}

interface DashboardStatsWidgetProps {
  data: DashboardStatsData | null;
  loading?: boolean;
  onRefresh?: () => void;
  timeframe?: string;
  compact?: boolean;
}

export const DashboardStatsWidget: React.FC<DashboardStatsWidgetProps> = ({
  data,
  loading = false,
  onRefresh,
  timeframe = '7d',
  compact = false
}) => {
  if (loading || !data) {
    return <DashboardWidgetSkeleton />;
  }

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSuccessRateVariant = (rate: number) => {
    if (rate >= 90) return 'default' as const;
    if (rate >= 70) return 'secondary' as const;
    return 'destructive' as const;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const getTimeframeLabel = (tf: string) => {
    switch (tf) {
      case '1d': return 'Last 24 hours';
      case '7d': return 'Last 7 days';
      case '30d': return 'Last 30 days';
      case '90d': return 'Last 90 days';
      default: return 'Custom period';
    }
  };

  if (compact) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-foreground">{data.totalTransformations}</div>
          <div className="text-sm text-muted-foreground">Total</div>
        </div>
        <div className="text-center">
          <div className={`text-2xl font-bold ${getSuccessRateColor(data.successRate)}`}>
            {data.successRate.toFixed(1)}%
          </div>
          <div className="text-sm text-muted-foreground">Success Rate</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-foreground">{formatCurrency(data.totalCost)}</div>
          <div className="text-sm text-muted-foreground">Total Cost</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-foreground">
            {formatDuration(data.averageExecutionTime)}
          </div>
          <div className="text-sm text-muted-foreground">Avg Time</div>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5" />
            <CardTitle>Dashboard Overview</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{getTimeframeLabel(timeframe)}</Badge>
            {onRefresh && (
              <Button size="sm" variant="ghost" onClick={onRefresh}>
                <RefreshCw className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
        <CardDescription>
          Performance metrics and transformation statistics
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <Activity className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium">Total</span>
            </div>
            <div className="text-2xl font-bold text-foreground">{data.totalTransformations}</div>
            <div className="text-xs text-muted-foreground">Transformations</div>
          </div>

          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium">Success Rate</span>
            </div>
            <div className={`text-2xl font-bold ${getSuccessRateColor(data.successRate)}`}>
              {data.successRate.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">
              {data.completedTransformations} completed
            </div>
          </div>

          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <DollarSign className="w-4 h-4 text-yellow-600" />
              <span className="text-sm font-medium">Total Cost</span>
            </div>
            <div className="text-2xl font-bold text-foreground">{formatCurrency(data.totalCost)}</div>
            <div className="text-xs text-muted-foreground">
              {data.totalTransformations > 0 
                ? `${formatCurrency(data.totalCost / data.totalTransformations)} avg`
                : 'No data'
              }
            </div>
          </div>

          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <Clock className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium">Avg Time</span>
            </div>
            <div className="text-2xl font-bold text-foreground">
              {formatDuration(data.averageExecutionTime)}
            </div>
            <div className="text-xs text-muted-foreground">Execution time</div>
          </div>
        </div>

        {/* Status Breakdown */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Status Breakdown</h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm">Completed</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{data.completedTransformations}</span>
                <div className="w-24">
                  <Progress 
                    value={(data.completedTransformations / data.totalTransformations) * 100} 
                    className="h-2 [&>div]:bg-green-500"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-blue-600" />
                <span className="text-sm">Running</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{data.runningTransformations}</span>
                <div className="w-24">
                  <Progress 
                    value={(data.runningTransformations / data.totalTransformations) * 100} 
                    className="h-2 [&>div]:bg-blue-500"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <XCircle className="w-4 h-4 text-red-600" />
                <span className="text-sm">Failed</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{data.failedTransformations}</span>
                <div className="w-24">
                  <Progress 
                    value={(data.failedTransformations / data.totalTransformations) * 100} 
                    className="h-2 [&>div]:bg-red-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quality Score */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Average Quality Score</span>
            <span className="text-sm font-mono">{data.averageScore.toFixed(1)}/100</span>
          </div>
          <Progress 
            value={data.averageScore} 
            className={`h-3 ${
              data.averageScore >= 80 ? '[&>div]:bg-green-500' :
              data.averageScore >= 60 ? '[&>div]:bg-yellow-500' :
              '[&>div]:bg-red-500'
            }`}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Poor (0-40)</span>
            <span>Good (40-80)</span>
            <span>Excellent (80-100)</span>
          </div>
        </div>

        {/* Recent Activity Summary */}
        {data.recentActivity && data.recentActivity.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Recent Activity</h4>
            <div className="text-sm text-muted-foreground">
              {data.recentActivity.length} recent transformations
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DashboardStatsWidget;
