import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Bell, 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  XCircle,
  X,
  ExternalLink,
  MarkAsRead,
  Trash2,
  Settings
} from 'lucide-react';
import { DashboardWidgetSkeleton } from '@/components/ui/loading-states';
import { useNotifications, useMutation } from '@/hooks/useApiService';
import { api } from '@/lib/api/services';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';

interface NotificationData {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'system';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  read: boolean;
  dismissible: boolean;
  action_url?: string;
  action_label?: string;
  created_at: string;
}

interface NotificationsWidgetProps {
  data?: NotificationData[];
  loading?: boolean;
  onRefresh?: () => void;
  compact?: boolean;
  maxItems?: number;
}

export const NotificationsWidget: React.FC<NotificationsWidgetProps> = ({
  data: propData,
  loading: propLoading,
  onRefresh,
  compact = false,
  maxItems = 10
}) => {
  const { toast } = useToast();
  const [showAll, setShowAll] = useState(false);
  
  // Use API hook if no data provided
  const { 
    data: apiData, 
    loading: apiLoading, 
    refresh 
  } = useNotifications({ 
    immediate: !propData 
  });

  // Mutation hooks
  const { mutate: markAsRead } = useMutation(
    (id: string) => api.notifications.markAsRead(id),
    {
      onSuccess: () => {
        refresh();
        toast({
          title: "Marked as read",
          description: "Notification has been marked as read",
        });
      },
      showToast: false
    }
  );

  const { mutate: deleteNotification } = useMutation(
    (id: string) => api.notifications.deleteNotification(id),
    {
      onSuccess: () => {
        refresh();
        toast({
          title: "Deleted",
          description: "Notification has been deleted",
        });
      },
      showToast: false
    }
  );

  const { mutate: markAllAsRead } = useMutation(
    () => api.notifications.markAllAsRead(),
    {
      onSuccess: () => {
        refresh();
        toast({
          title: "All marked as read",
          description: "All notifications have been marked as read",
        });
      },
      showToast: false
    }
  );

  const data = propData || apiData || [];
  const loading = propLoading || apiLoading;

  if (loading) {
    return <DashboardWidgetSkeleton />;
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-600" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'info': return <Info className="w-4 h-4 text-blue-600" />;
      case 'system': return <Settings className="w-4 h-4 text-purple-600" />;
      default: return <Bell className="w-4 h-4 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'border-l-red-500 bg-red-50 dark:bg-red-950';
      case 'high': return 'border-l-orange-500 bg-orange-50 dark:bg-orange-950';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-950';
      case 'low': return 'border-l-blue-500 bg-blue-50 dark:bg-blue-950';
      default: return 'border-l-gray-500 bg-gray-50 dark:bg-gray-950';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'critical': return 'destructive' as const;
      case 'high': return 'secondary' as const;
      case 'medium': return 'outline' as const;
      case 'low': return 'outline' as const;
      default: return 'outline' as const;
    }
  };

  const unreadCount = data.filter(n => !n.read).length;
  const displayData = showAll ? data : data.slice(0, maxItems);

  const handleMarkAsRead = (id: string) => {
    markAsRead(id);
  };

  const handleDelete = (id: string) => {
    deleteNotification(id);
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const handleActionClick = (notification: NotificationData) => {
    if (notification.action_url) {
      window.open(notification.action_url, '_blank');
    }
    if (!notification.read) {
      handleMarkAsRead(notification.id);
    }
  };

  if (compact) {
    return (
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Bell className="w-4 h-4" />
          <span className="text-sm font-medium">Notifications</span>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {unreadCount}
            </Badge>
          )}
        </div>
        
        {data.length > 0 && (
          <div className="text-sm text-muted-foreground">
            Latest: {formatDistanceToNow(new Date(data[0].created_at), { addSuffix: true })}
          </div>
        )}
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell className="w-5 h-5" />
            <CardTitle>Notifications</CardTitle>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {unreadCount} unread
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <Button size="sm" variant="outline" onClick={handleMarkAllAsRead}>
                Mark all read
              </Button>
            )}
            {onRefresh && (
              <Button size="sm" variant="ghost" onClick={onRefresh}>
                <Bell className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
        <CardDescription>
          Recent system notifications and alerts
        </CardDescription>
      </CardHeader>
      <CardContent>
        {data.length === 0 ? (
          <div className="text-center py-8">
            <Bell className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">No notifications</p>
          </div>
        ) : (
          <div className="space-y-4">
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {displayData.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-3 rounded-lg border-l-4 ${getPriorityColor(notification.priority)} ${
                      !notification.read ? 'ring-1 ring-blue-200 dark:ring-blue-800' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between space-x-3">
                      <div className="flex items-start space-x-3 flex-1">
                        {getNotificationIcon(notification.type)}
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center space-x-2">
                            <h4 className={`text-sm font-medium ${!notification.read ? 'font-semibold' : ''}`}>
                              {notification.title}
                            </h4>
                            <Badge variant={getPriorityBadgeVariant(notification.priority)} className="text-xs">
                              {notification.priority}
                            </Badge>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full" />
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-muted-foreground">
                              {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                            </span>
                            <div className="flex items-center space-x-1">
                              {notification.action_url && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleActionClick(notification)}
                                  className="h-6 px-2 text-xs"
                                >
                                  <ExternalLink className="w-3 h-3 mr-1" />
                                  {notification.action_label || 'View'}
                                </Button>
                              )}
                              {!notification.read && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleMarkAsRead(notification.id)}
                                  className="h-6 px-2 text-xs"
                                >
                                  <CheckCircle className="w-3 h-3" />
                                </Button>
                              )}
                              {notification.dismissible && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleDelete(notification.id)}
                                  className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            {data.length > maxItems && (
              <div className="text-center pt-2 border-t">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAll(!showAll)}
                >
                  {showAll ? 'Show less' : `Show all ${data.length} notifications`}
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default NotificationsWidget;
