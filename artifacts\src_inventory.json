{"summary": {"totalSourceFiles": 1161, "totalDependencies": 1638, "productionDependencies": 635, "devDependencies": 959, "vulnerabilities": {"total": 3, "moderate": 3, "high": 0, "critical": 0}}, "architecture": {"type": "TypeScript Monorepo", "structure": "Workspaces (apps/*, packages/*)", "mainApps": ["apps/web - React/Vite frontend", "apps/api - Express.js backend"], "packages": ["packages/agents - Dual-agent system core"]}, "codeQuality": {"eslintViolations": {"total": 16, "errors": 15, "warnings": 1, "primaryIssues": ["TypeScript 'any' type usage in test files", "Missing React Hook dependencies", "Fatal parsing error in integration test"]}, "typeScriptConfig": {"strictMode": "Partially enabled", "noImplicitAny": false, "strictNullChecks": false, "skipLibCheck": true}}, "security": {"vulnerabilities": [{"package": "esbuild", "severity": "moderate", "cvss": 5.3, "description": "Development server vulnerability", "impact": "Development only, not production"}, {"package": "vite", "severity": "moderate", "description": "Affected by esbuild vulnerability"}, {"package": "lovable-tagger", "severity": "moderate", "description": "Affected by vite vulnerability"}], "riskAssessment": "LOW - All vulnerabilities are development-only"}, "testing": {"frameworks": ["Jest", "Playwright", "axe-core"], "coverageTargets": {"global": "85%", "providers": "90%", "utils": "88%"}, "testTypes": ["Unit tests (Jest)", "Integration tests", "E2E tests (Playwright)", "Accessibility tests (axe-core)"]}, "buildTools": {"frontend": "Vite", "backend": "TypeScript compiler", "bundling": "Rollup (via Vite)", "optimization": "Manual chunk splitting configured"}, "recommendations": ["Enable stricter TypeScript settings (noImplicitAny, strictNullChecks)", "Fix ESLint violations in test files", "Monitor esbuild updates for security patches", "Consider removing lovable-tagger if not essential", "Implement proper type definitions instead of 'any' usage"]}