import { ErrorHand<PERSON>, ErrorCategory } from '@/lib/errors/errorHandler';

export interface WebSocketMessage {
  type: string;
  data: any;
  id?: string;
  timestamp?: number;
}

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  enableHeartbeat: boolean;
  enableLogging: boolean;
}

export enum WebSocketState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting',
}

export type WebSocketEventHandler = (message: WebSocketMessage) => void;
export type WebSocketStateHandler = (state: WebSocketState) => void;

export class WebSocketApiService {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private state: WebSocketState = WebSocketState.DISCONNECTED;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private stateHandlers: WebSocketStateHandler[] = [];
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private messageQueue: WebSocketMessage[] = [];

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: import.meta.env.VITE_WS_URL || 'ws://localhost:3001',
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      enableHeartbeat: true,
      enableLogging: false,
      ...config,
    };
  }

  // Connection management
  async connect(): Promise<void> {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    this.setState(WebSocketState.CONNECTING);

    try {
      this.ws = new WebSocket(this.config.url, this.config.protocols);
      this.setupEventListeners();
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);

        this.ws!.onopen = () => {
          clearTimeout(timeout);
          this.onConnected();
          resolve();
        };

        this.ws!.onerror = (error) => {
          clearTimeout(timeout);
          reject(error);
        };
      });
    } catch (error) {
      this.handleError(error as Error);
      throw error;
    }
  }

  disconnect(): void {
    this.clearTimers();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.setState(WebSocketState.DISCONNECTED);
  }

  // Message sending
  send(type: string, data: any): boolean {
    const message: WebSocketMessage = {
      type,
      data,
      id: this.generateMessageId(),
      timestamp: Date.now(),
    };

    if (this.state !== WebSocketState.CONNECTED) {
      // Queue message for later sending
      this.messageQueue.push(message);
      this.log('Message queued (not connected):', message);
      return false;
    }

    try {
      this.ws!.send(JSON.stringify(message));
      this.log('Message sent:', message);
      return true;
    } catch (error) {
      this.handleError(error as Error);
      return false;
    }
  }

  // Event subscription
  on(eventType: string, handler: WebSocketEventHandler): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    
    this.eventHandlers.get(eventType)!.push(handler);
    
    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(eventType);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  // State subscription
  onStateChange(handler: WebSocketStateHandler): () => void {
    this.stateHandlers.push(handler);
    
    return () => {
      const index = this.stateHandlers.indexOf(handler);
      if (index > -1) {
        this.stateHandlers.splice(index, 1);
      }
    };
  }

  // Getters
  getState(): WebSocketState {
    return this.state;
  }

  isConnected(): boolean {
    return this.state === WebSocketState.CONNECTED;
  }

  getQueuedMessageCount(): number {
    return this.messageQueue.length;
  }

  // Private methods
  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = () => this.onConnected();
    this.ws.onclose = (event) => this.onDisconnected(event);
    this.ws.onerror = (error) => this.handleError(error as any);
    this.ws.onmessage = (event) => this.onMessage(event);
  }

  private onConnected(): void {
    this.setState(WebSocketState.CONNECTED);
    this.reconnectAttempts = 0;
    
    // Send queued messages
    this.sendQueuedMessages();
    
    // Start heartbeat
    if (this.config.enableHeartbeat) {
      this.startHeartbeat();
    }
    
    this.log('WebSocket connected');
  }

  private onDisconnected(event: CloseEvent): void {
    this.clearTimers();
    
    if (event.wasClean) {
      this.setState(WebSocketState.DISCONNECTED);
      this.log('WebSocket disconnected cleanly');
    } else {
      this.setState(WebSocketState.ERROR);
      this.log('WebSocket disconnected unexpectedly:', event);
      this.attemptReconnect();
    }
  }

  private onMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      this.log('Message received:', message);
      
      // Handle heartbeat response
      if (message.type === 'pong') {
        return;
      }
      
      // Emit to event handlers
      const handlers = this.eventHandlers.get(message.type) || [];
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in WebSocket event handler:', error);
        }
      });
      
      // Emit to wildcard handlers
      const wildcardHandlers = this.eventHandlers.get('*') || [];
      wildcardHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in WebSocket wildcard handler:', error);
        }
      });
      
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.setState(WebSocketState.ERROR);
      this.log('Max reconnect attempts reached');
      return;
    }

    this.setState(WebSocketState.RECONNECTING);
    this.reconnectAttempts++;
    
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
      30000
    );
    
    this.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(error => {
        this.log('Reconnect failed:', error);
        this.attemptReconnect();
      });
    }, delay);
  }

  private sendQueuedMessages(): void {
    while (this.messageQueue.length > 0 && this.state === WebSocketState.CONNECTED) {
      const message = this.messageQueue.shift()!;
      try {
        this.ws!.send(JSON.stringify(message));
        this.log('Queued message sent:', message);
      } catch (error) {
        // Put message back in queue
        this.messageQueue.unshift(message);
        break;
      }
    }
  }

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.state === WebSocketState.CONNECTED) {
        this.send('ping', { timestamp: Date.now() });
      }
    }, this.config.heartbeatInterval);
  }

  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private setState(newState: WebSocketState): void {
    if (this.state !== newState) {
      this.state = newState;
      this.stateHandlers.forEach(handler => {
        try {
          handler(newState);
        } catch (error) {
          console.error('Error in WebSocket state handler:', error);
        }
      });
    }
  }

  private handleError(error: Error): void {
    this.log('WebSocket error:', error);
    
    ErrorHandler.createError(
      ErrorCategory.NETWORK,
      'WebSocket connection error',
      {
        details: error.message,
        originalError: error,
        retryable: true,
      }
    );
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private log(message: string, data?: any): void {
    if (this.config.enableLogging) {
      console.log(`[WebSocket] ${message}`, data || '');
    }
  }
}

// Create singleton instance
export const websocketService = new WebSocketApiService({
  enableLogging: process.env.NODE_ENV === 'development',
});

// Auto-connect in browser environment
if (typeof window !== 'undefined') {
  // Connect when the service is imported
  websocketService.connect().catch(error => {
    console.warn('Failed to auto-connect WebSocket:', error);
  });
  
  // Reconnect when the page becomes visible
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden && websocketService.getState() === WebSocketState.DISCONNECTED) {
      websocketService.connect().catch(error => {
        console.warn('Failed to reconnect WebSocket on visibility change:', error);
      });
    }
  });
}
