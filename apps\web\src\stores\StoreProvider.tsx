import React, { createContext, useContext, useEffect } from 'react';
import { useAppStore } from './appStore';
import { useTransformationStore } from './transformationStore';
import { useSettingsStore } from './settingsStore';
import { useToast } from '@/hooks/use-toast';

// Store context type
interface StoreContextType {
  appStore: typeof useAppStore;
  transformationStore: typeof useTransformationStore;
  settingsStore: typeof useSettingsStore;
}

// Create context
const StoreContext = createContext<StoreContextType | null>(null);

// Store provider component
interface StoreProviderProps {
  children: React.ReactNode;
}

export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  const { toast } = useToast();

  // Initialize stores and set up subscriptions
  useEffect(() => {
    // Subscribe to transformation status changes for notifications
    const unsubscribeTransformation = useTransformationStore.subscribe(
      (state) => state.currentJob?.status,
      (status, previousStatus) => {
        if (status && status !== previousStatus) {
          const currentJob = useTransformationStore.getState().currentJob;
          if (!currentJob) return;

          switch (status) {
            case 'completed':
              toast({
                title: "Transformation Complete",
                description: `Job ${currentJob.id.slice(-8)} completed with score ${currentJob.finalScore?.toFixed(2) || 'N/A'}`,
              });
              break;
            case 'failed':
              toast({
                title: "Transformation Failed",
                description: `Job ${currentJob.id.slice(-8)} failed: ${currentJob.error || 'Unknown error'}`,
                variant: "destructive",
              });
              break;
            case 'cancelled':
              toast({
                title: "Transformation Cancelled",
                description: `Job ${currentJob.id.slice(-8)} was cancelled`,
                variant: "destructive",
              });
              break;
          }
        }
      }
    );

    // Subscribe to cost guard alerts
    const unsubscribeCostGuard = useTransformationStore.subscribe(
      (state) => state.queue.totalCost,
      (totalCost) => {
        const costGuard = useSettingsStore.getState().costGuard;
        if (!costGuard.enabled) return;

        const costPercentage = (totalCost / costGuard.maxCostPerDay) * 100;
        
        if (costPercentage >= costGuard.alertThresholds.nearLimitWarning) {
          toast({
            title: "Cost Alert",
            description: `You've used ${costPercentage.toFixed(1)}% of your daily cost limit ($${totalCost.toFixed(2)}/$${costGuard.maxCostPerDay})`,
            variant: "destructive",
          });
        }
      }
    );

    // Subscribe to settings validation
    const unsubscribeSettings = useSettingsStore.subscribe(
      (state) => state.lastUpdated,
      () => {
        const validation = useSettingsStore.getState().validateSettings();
        if (!validation.valid) {
          console.warn('Settings validation failed:', validation.errors);
        }
      }
    );

    // Cleanup subscriptions
    return () => {
      unsubscribeTransformation();
      unsubscribeCostGuard();
      unsubscribeSettings();
    };
  }, [toast]);

  // Sync app store with settings store for theme changes
  useEffect(() => {
    const unsubscribe = useSettingsStore.subscribe(
      (state) => state.ui,
      (uiSettings) => {
        // Sync theme and other UI settings
        const appStore = useAppStore.getState();
        if (appStore.preferences.theme !== uiSettings.defaultView) {
          // Handle theme sync if needed
        }
      }
    );

    return unsubscribe;
  }, []);

  const contextValue: StoreContextType = {
    appStore: useAppStore,
    transformationStore: useTransformationStore,
    settingsStore: useSettingsStore,
  };

  return (
    <StoreContext.Provider value={contextValue}>
      {children}
    </StoreContext.Provider>
  );
};

// Hook to use stores
export const useStores = () => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error('useStores must be used within a StoreProvider');
  }
  return context;
};

// Individual store hooks for convenience
export const useAppState = () => {
  const { appStore } = useStores();
  return appStore();
};

export const useTransformationState = () => {
  const { transformationStore } = useStores();
  return transformationStore();
};

export const useSettingsState = () => {
  const { settingsStore } = useStores();
  return settingsStore();
};

// Selector hooks for optimized subscriptions
export const useAppSelector = <T,>(selector: (state: ReturnType<typeof useAppStore>) => T) => {
  return useAppStore(selector);
};

export const useTransformationSelector = <T,>(selector: (state: ReturnType<typeof useTransformationStore>) => T) => {
  return useTransformationStore(selector);
};

export const useSettingsSelector = <T,>(selector: (state: ReturnType<typeof useSettingsStore>) => T) => {
  return useSettingsStore(selector);
};

// Utility hooks for common operations
export const useUIActions = () => {
  return useAppStore((state) => ({
    toggleSidebar: state.toggleSidebar,
    setSidebarOpen: state.setSidebarOpen,
    setActivePanel: state.setActivePanel,
    toggleExamples: state.toggleExamples,
    toggleHelp: state.toggleHelp,
    toggleOnboarding: state.toggleOnboarding,
    setMobileMenuOpen: state.setMobileMenuOpen,
    setMobilePanelOpen: state.setMobilePanelOpen,
    toggleCommandPalette: state.toggleCommandPalette,
    setCurrentTransformationId: state.setCurrentTransformationId,
  }));
};

export const useTransformationActions = () => {
  return useTransformationStore((state) => ({
    addJob: state.addJob,
    removeJob: state.removeJob,
    startJob: state.startJob,
    cancelJob: state.cancelJob,
    updateJobProgress: state.updateJobProgress,
    addAgentLog: state.addAgentLog,
    updateJobStatus: state.updateJobStatus,
  }));
};

export const useSettingsActions = () => {
  return useSettingsStore((state) => ({
    updatePlannerProvider: state.updatePlannerProvider,
    updateCriticProvider: state.updateCriticProvider,
    setDefaultMaxIterations: state.setDefaultMaxIterations,
    setDefaultScoreThreshold: state.setDefaultScoreThreshold,
    updateCostGuard: state.updateCostGuard,
    updateNotificationSettings: state.updateNotificationSettings,
    updateUISettings: state.updateUISettings,
    updateShortcut: state.updateShortcut,
  }));
};

// State persistence utilities
export const useStatePersistence = () => {
  const exportAllState = () => {
    return {
      app: useAppStore.getState().exportState(),
      transformations: useTransformationStore.getState().exportHistory(),
      settings: useSettingsStore.getState().exportSettings(),
      timestamp: new Date().toISOString(),
    };
  };

  const importAllState = (data: any) => {
    try {
      if (data.app) {
        useAppStore.getState().importState(data.app);
      }
      if (data.transformations) {
        useTransformationStore.getState().importHistory(data.transformations);
      }
      if (data.settings) {
        useSettingsStore.getState().importSettings(data.settings);
      }
      return true;
    } catch (error) {
      console.error('Failed to import state:', error);
      return false;
    }
  };

  const resetAllState = () => {
    useAppStore.getState().resetState();
    useTransformationStore.getState().resetStore();
    useSettingsStore.getState().resetToDefaults();
  };

  return {
    exportAllState,
    importAllState,
    resetAllState,
  };
};

// Performance monitoring hook
export const useStorePerformance = () => {
  const [metrics, setMetrics] = React.useState({
    appStoreUpdates: 0,
    transformationStoreUpdates: 0,
    settingsStoreUpdates: 0,
    lastUpdate: Date.now(),
  });

  useEffect(() => {
    let updateCount = 0;

    const unsubscribeApp = useAppStore.subscribe(() => {
      updateCount++;
      setMetrics(prev => ({
        ...prev,
        appStoreUpdates: prev.appStoreUpdates + 1,
        lastUpdate: Date.now(),
      }));
    });

    const unsubscribeTransformation = useTransformationStore.subscribe(() => {
      updateCount++;
      setMetrics(prev => ({
        ...prev,
        transformationStoreUpdates: prev.transformationStoreUpdates + 1,
        lastUpdate: Date.now(),
      }));
    });

    const unsubscribeSettings = useSettingsStore.subscribe(() => {
      updateCount++;
      setMetrics(prev => ({
        ...prev,
        settingsStoreUpdates: prev.settingsStoreUpdates + 1,
        lastUpdate: Date.now(),
      }));
    });

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(() => {
        if (updateCount > 0) {
          console.log(`Store updates in last 5s: ${updateCount}`);
          updateCount = 0;
        }
      }, 5000);

      return () => {
        clearInterval(interval);
        unsubscribeApp();
        unsubscribeTransformation();
        unsubscribeSettings();
      };
    }

    return () => {
      unsubscribeApp();
      unsubscribeTransformation();
      unsubscribeSettings();
    };
  }, []);

  return metrics;
};

export default StoreProvider;
