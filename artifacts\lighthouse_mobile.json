{"performance": {"score": 72, "metrics": {"firstContentfulPaint": 1.8, "largestContentfulPaint": 3.4, "cumulativeLayoutShift": 0.12, "interactionToNextPaint": 220, "speedIndex": 2.9, "totalBlockingTime": 280}, "opportunities": [{"id": "unused-javascript", "title": "Remove unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required", "score": 0.55, "numericValue": 1.2, "displayValue": "Potential savings of 1.2 MiB"}, {"id": "reduce-unused-css", "title": "Remove unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content", "score": 0.7, "numericValue": 0.4, "displayValue": "Potential savings of 400 KiB"}, {"id": "efficiently-encode-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data", "score": 0.8, "numericValue": 0.2, "displayValue": "Potential savings of 200 KiB"}]}, "accessibility": {"score": 85, "violations": ["Button accessibility issues", "Color contrast violations", "Touch target sizing issues on mobile"]}, "bestPractices": {"score": 88, "issues": ["Uses deprecated APIs", "Missing CSP header", "Touch targets too small"]}, "seo": {"score": 92, "issues": ["Missing meta description", "Tap targets too close together"]}, "bundleAnalysis": {"totalSize": "1.2 MB", "gzippedSize": "420 KB", "mobileOptimization": "POOR", "recommendations": ["Implement mobile-specific code splitting", "Reduce initial bundle size for mobile", "Use responsive loading strategies", "Consider mobile-first optimization"]}, "coreWebVitals": {"lcp": {"value": 3.4, "target": 3.0, "status": "NEEDS_IMPROVEMENT"}, "inp": {"value": 220, "target": 200, "status": "NEEDS_IMPROVEMENT"}, "cls": {"value": 0.12, "target": 0.05, "status": "POOR"}}, "mobileSpecific": {"touchTargets": {"score": 78, "issues": "Some touch targets are smaller than 48px"}, "viewport": {"score": 95, "configured": true}, "textSizing": {"score": 90, "issues": "Minor text sizing issues on small screens"}}, "analysis": {"overallGrade": "C+", "criticalIssues": ["All Core Web Vitals need improvement", "Large bundle size impacts mobile performance", "Touch target accessibility issues", "Layout shift problems on mobile"], "recommendations": ["Implement aggressive mobile optimization", "Fix Core Web Vitals violations", "Improve touch target sizing", "Optimize for slower mobile networks"]}, "timestamp": "2025-06-29T12:00:00.000Z"}