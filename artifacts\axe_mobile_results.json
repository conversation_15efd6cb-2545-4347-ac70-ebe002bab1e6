{"accessibility": {"Mobile": {"score": 85, "violations": {"critical": 1, "serious": 1, "moderate": 0, "minor": 0, "total": 2}, "details": [{"id": "button-name", "impact": "critical", "description": "Ensure buttons have discernible text", "help": "Buttons must have discernible text", "nodes": 2}, {"id": "color-contrast", "impact": "serious", "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "nodes": 4}]}, "Tablet": {"score": 85, "violations": {"critical": 1, "serious": 1, "moderate": 0, "minor": 0, "total": 2}, "details": [{"id": "aria-valid-attr-value", "impact": "critical", "description": "Ensure all ARIA attributes have valid values", "help": "ARIA attributes must conform to valid values", "nodes": 2}, {"id": "color-contrast", "impact": "serious", "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "nodes": 11}]}}, "analysis": {"complianceLevel": "BELOW_TARGET", "targetScore": 97, "actualScore": 85, "gap": -12, "riskLevel": "MEDIUM", "criticalIssues": ["Button accessibility issues affecting 2 elements", "Color contrast violations affecting 4-11 elements across viewports", "ARIA attribute validation failures"], "recommendations": ["Add proper button labels and accessible names", "Improve color contrast ratios across all viewport sizes", "Validate and fix ARIA attribute implementations", "Test responsive design accessibility at all breakpoints"]}, "timestamp": "2025-06-19T20:49:09.953Z"}