{"performance": {"score": 78, "metrics": {"firstContentfulPaint": 1.2, "largestContentfulPaint": 2.8, "cumulativeLayoutShift": 0.08, "interactionToNextPaint": 180, "speedIndex": 2.1, "totalBlockingTime": 150}, "opportunities": [{"id": "unused-javascript", "title": "Remove unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required", "score": 0.65, "numericValue": 0.8, "displayValue": "Potential savings of 800 KiB"}, {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP often provide better compression than PNG or JPEG", "score": 0.8, "numericValue": 0.3, "displayValue": "Potential savings of 300 KiB"}]}, "accessibility": {"score": 85, "violations": ["Color contrast issues", "Missing button labels", "Invalid ARIA attributes"]}, "bestPractices": {"score": 92, "issues": ["Uses deprecated APIs", "Missing CSP header"]}, "seo": {"score": 95, "issues": ["Missing meta description on some pages"]}, "bundleAnalysis": {"totalSize": "1.2 MB", "gzippedSize": "420 KB", "chunks": {"vendor-react": "180 KB", "vendor-monaco": "650 KB", "vendor-ui": "120 KB", "main": "250 KB"}, "recommendations": ["Monaco Editor is the largest chunk - consider lazy loading", "Implement code splitting for dashboard features", "Optimize vendor chunk sizes", "Consider using dynamic imports for heavy components"]}, "coreWebVitals": {"lcp": {"value": 2.8, "target": 3.0, "status": "GOOD"}, "inp": {"value": 180, "target": 200, "status": "GOOD"}, "cls": {"value": 0.08, "target": 0.05, "status": "NEEDS_IMPROVEMENT"}}, "analysis": {"overallGrade": "B+", "criticalIssues": ["CLS exceeds target threshold", "Large JavaScript bundles", "Accessibility score below target (85 vs 97)"], "recommendations": ["Implement lazy loading for Monaco Editor", "Fix layout shift issues", "Optimize bundle splitting strategy", "Improve accessibility compliance"]}, "timestamp": "2025-06-29T12:00:00.000Z"}