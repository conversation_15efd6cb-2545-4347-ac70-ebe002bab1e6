import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from './button';
import { Badge } from './badge';
import { ScrollArea } from './scroll-area';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { 
  Bell, 
  BellRing, 
  Check, 
  X, 
  Trash2, 
  Settings,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Clock
} from 'lucide-react';
import { useAppSelector, useAppState } from '@/stores/StoreProvider';
import { formatDistanceToNow } from 'date-fns';

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  persistent?: boolean;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'ghost';
  }>;
}

// Notification icon component
const NotificationIcon: React.FC<{ type: Notification['type']; className?: string }> = ({ 
  type, 
  className 
}) => {
  const iconClass = cn("w-4 h-4", className);
  
  switch (type) {
    case 'success':
      return <CheckCircle className={cn(iconClass, "text-green-600")} />;
    case 'error':
      return <XCircle className={cn(iconClass, "text-red-600")} />;
    case 'warning':
      return <AlertTriangle className={cn(iconClass, "text-yellow-600")} />;
    case 'info':
      return <Info className={cn(iconClass, "text-blue-600")} />;
    default:
      return <Bell className={cn(iconClass, "text-gray-600")} />;
  }
};

// Individual notification item
const NotificationItem: React.FC<{
  notification: Notification;
  onMarkRead: (id: string) => void;
  onRemove: (id: string) => void;
  compact?: boolean;
}> = ({ notification, onMarkRead, onRemove, compact = false }) => {
  const handleMarkRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkRead(notification.id);
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRemove(notification.id);
  };

  return (
    <div
      className={cn(
        "group relative p-3 border-b border-border/50 transition-colors hover:bg-muted/50",
        !notification.read && "bg-blue-50/50 border-l-4 border-l-blue-500",
        compact && "p-2"
      )}
    >
      <div className="flex items-start space-x-3">
        <NotificationIcon type={notification.type} className="mt-0.5 flex-shrink-0" />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h4 className={cn(
                "text-sm font-medium truncate",
                !notification.read && "font-semibold"
              )}>
                {notification.title}
              </h4>
              
              {!compact && (
                <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                  {notification.message}
                </p>
              )}
              
              <div className="flex items-center space-x-2 mt-2">
                <span className="text-xs text-muted-foreground">
                  {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                </span>
                
                {!notification.read && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                    New
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {!notification.read && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleMarkRead}
                  className="h-6 w-6 p-0"
                  title="Mark as read"
                >
                  <Check className="w-3 h-3" />
                </Button>
              )}
              
              <Button
                size="sm"
                variant="ghost"
                onClick={handleRemove}
                className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                title="Remove notification"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>
          
          {/* Notification actions */}
          {notification.actions && notification.actions.length > 0 && (
            <div className="flex items-center space-x-2 mt-3">
              {notification.actions.map((action, index) => (
                <Button
                  key={index}
                  size="sm"
                  variant={action.variant || 'outline'}
                  onClick={action.onClick}
                  className="h-7 px-2 text-xs"
                >
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Notification center component
export const NotificationCenter: React.FC<{
  className?: string;
  compact?: boolean;
}> = ({ className, compact = false }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  
  // Get notifications from store
  const { notifications, unreadCount } = useAppSelector((state) => state.notifications);
  const { markNotificationRead, markAllNotificationsRead, removeNotification, clearNotifications } = useAppState();

  // Sort notifications by timestamp (newest first)
  const sortedNotifications = React.useMemo(() => {
    return [...notifications].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }, [notifications]);

  const unreadNotifications = sortedNotifications.filter(n => !n.read);
  const readNotifications = sortedNotifications.filter(n => n.read);

  const handleMarkAllRead = () => {
    markAllNotificationsRead();
  };

  const handleClearAll = () => {
    clearNotifications();
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn("relative", className)}
          aria-label={`Notifications (${unreadCount} unread)`}
        >
          {unreadCount > 0 ? (
            <BellRing className="w-5 h-5" />
          ) : (
            <Bell className="w-5 h-5" />
          )}
          
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent
        className="w-80 p-0"
        align="end"
        sideOffset={8}
      >
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold text-sm">Notifications</h3>
          
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleMarkAllRead}
                className="h-7 px-2 text-xs"
              >
                Mark all read
              </Button>
            )}
            
            <Button
              size="sm"
              variant="ghost"
              onClick={handleClearAll}
              className="h-7 w-7 p-0"
              title="Clear all notifications"
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
        
        {notifications.length === 0 ? (
          <div className="p-8 text-center">
            <Bell className="w-8 h-8 mx-auto text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">No notifications</p>
          </div>
        ) : (
          <ScrollArea className="h-96">
            <div className="divide-y">
              {/* Unread notifications */}
              {unreadNotifications.length > 0 && (
                <div>
                  {unreadNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkRead={markNotificationRead}
                      onRemove={removeNotification}
                      compact={compact}
                    />
                  ))}
                </div>
              )}
              
              {/* Read notifications */}
              {readNotifications.length > 0 && unreadNotifications.length > 0 && (
                <div className="px-3 py-2 bg-muted/30">
                  <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                    Earlier
                  </span>
                </div>
              )}
              
              {readNotifications.slice(0, 20).map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkRead={markNotificationRead}
                  onRemove={removeNotification}
                  compact={compact}
                />
              ))}
            </div>
          </ScrollArea>
        )}
        
        {notifications.length > 20 && (
          <div className="p-3 border-t text-center">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                setIsOpen(false);
                // Navigate to full notifications page
                window.open('/notifications', '_blank');
              }}
              className="text-xs"
            >
              View all notifications
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};

// Notification settings component
export const NotificationSettings: React.FC = () => {
  const { notifications: notificationSettings } = useAppSelector((state) => state);
  const { updateNotificationSettings } = useAppState();

  const handleToggleSetting = (
    category: 'email' | 'browser' | 'sound',
    setting: string,
    value: boolean
  ) => {
    updateNotificationSettings({
      [category]: {
        ...notificationSettings[category],
        [setting]: value,
      },
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Notification Preferences</h3>
        <p className="text-sm text-muted-foreground">
          Choose how you want to be notified about important events.
        </p>
      </div>
      
      <div className="space-y-4">
        {/* Email notifications */}
        <div className="space-y-3">
          <h4 className="font-medium">Email Notifications</h4>
          <div className="space-y-2">
            {Object.entries(notificationSettings.email).map(([key, value]) => {
              if (key === 'enabled') return null;
              
              return (
                <label key={key} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={value as boolean}
                    onChange={(e) => handleToggleSetting('email', key, e.target.checked)}
                    disabled={!notificationSettings.email.enabled}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm capitalize">
                    {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                  </span>
                </label>
              );
            })}
          </div>
        </div>
        
        {/* Browser notifications */}
        <div className="space-y-3">
          <h4 className="font-medium">Browser Notifications</h4>
          <div className="space-y-2">
            {Object.entries(notificationSettings.browser).map(([key, value]) => {
              if (key === 'enabled') return null;
              
              return (
                <label key={key} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={value as boolean}
                    onChange={(e) => handleToggleSetting('browser', key, e.target.checked)}
                    disabled={!notificationSettings.browser.enabled}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm capitalize">
                    {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                  </span>
                </label>
              );
            })}
          </div>
        </div>
        
        {/* Sound notifications */}
        <div className="space-y-3">
          <h4 className="font-medium">Sound Notifications</h4>
          <div className="space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={notificationSettings.sound.enabled}
                onChange={(e) => handleToggleSetting('sound', 'enabled', e.target.checked)}
                className="rounded border-gray-300"
              />
              <span className="text-sm">Enable sound notifications</span>
            </label>
            
            {notificationSettings.sound.enabled && (
              <div className="ml-6 space-y-2">
                <label className="flex items-center space-x-2">
                  <span className="text-sm">Volume:</span>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={notificationSettings.sound.volume}
                    onChange={(e) => handleToggleSetting('sound', 'volume', parseFloat(e.target.value))}
                    className="flex-1"
                  />
                  <span className="text-sm w-8">
                    {Math.round(notificationSettings.sound.volume * 100)}%
                  </span>
                </label>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationCenter;
