
import React, { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/theme-provider";
import { AppProvider } from "@/contexts/AppContext";
import { StoreProvider } from "@/stores/StoreProvider";
import { FocusManager } from "@/components/ui/skip-links";
import { PageErrorBoundary, setupGlobalErrorHandling } from "@/components/ErrorBoundary";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { initializeWCAGFocusManagement } from "@/utils/accessibility";
import { BrowserRouter, Routes, Route } from "react-router-dom";
// Lazy load all pages for better code splitting
const LazyIndex = React.lazy(() => import("./pages/Index"));
const LazyNotFound = React.lazy(() => import("./pages/NotFound"));
const LazyCommandPalette = React.lazy(() => import("./components/CommandPalette").then(m => ({ default: m.CommandPalette })));

import {
  LazyDashboardWithSkeleton,
  LazyMonitoringDashboardWithSkeleton,
  LazySettings,
  LazyHistory,
  LazyWrapper,
  initializePerformanceOptimizations
} from "./components/LazyComponents";
import { LoadingSpinner } from "./components/ui/loading-states";

const queryClient = new QueryClient();

// Initialize performance optimizations
initializePerformanceOptimizations();

// Setup global error handling
setupGlobalErrorHandling();

// Initialize WCAG 2.2 focus management
initializeWCAGFocusManagement();

const App = () => (
  <PageErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <StoreProvider>
        <AppProvider>
          <ThemeProvider defaultTheme="dark" storageKey="metamorphic-reactor-theme">
          <TooltipProvider>
          <Toaster />
          <Sonner />
          <Suspense fallback={<LoadingSpinner message="Loading command palette..." />}>
            <LazyCommandPalette />
          </Suspense>
          <FocusManager>
            <BrowserRouter>
              <Routes>
              <Route
                path="/"
                element={
                  <Suspense fallback={<LoadingSpinner message="Loading..." />}>
                    <LazyIndex />
                  </Suspense>
                }
              />
              <Route path="/dashboard" element={<LazyDashboardWithSkeleton />} />
              <Route path="/monitoring" element={<LazyMonitoringDashboardWithSkeleton />} />
              <Route
                path="/settings"
                element={
                  <LazyWrapper fallback={<LoadingSpinner message="Loading settings..." />}>
                    <LazySettings />
                  </LazyWrapper>
                }
              />
              <Route
                path="/history"
                element={
                  <LazyWrapper fallback={<LoadingSpinner message="Loading history..." />}>
                    <LazyHistory />
                  </LazyWrapper>
                }
              />
              <Route
                path="*"
                element={
                  <Suspense fallback={<LoadingSpinner message="Page not found..." />}>
                    <LazyNotFound />
                  </Suspense>
                }
              />
              </Routes>
            </BrowserRouter>
          </FocusManager>
        </TooltipProvider>
      </ThemeProvider>
    </AppProvider>
      </StoreProvider>
  </QueryClientProvider>
  </PageErrorBoundary>
);

export default App;
