import React from 'react';
import { cn } from '@/lib/utils';
import { Check, X, AlertTriangle, Info, Eye, EyeOff } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

// Types
export interface ValidationState {
  isValid: boolean;
  message?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
}

export interface InteractiveInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
  validation?: ValidationState;
  showValidation?: boolean;
  realTimeValidation?: boolean;
  onValidationChange?: (validation: ValidationState) => void;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  showPasswordToggle?: boolean;
  characterLimit?: number;
  showCharacterCount?: boolean;
  animatedLabel?: boolean;
  focusRing?: boolean;
  successMessage?: string;
  errorMessage?: string;
}

export interface InteractiveTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  description?: string;
  validation?: ValidationState;
  showValidation?: boolean;
  realTimeValidation?: boolean;
  onValidationChange?: (validation: ValidationState) => void;
  characterLimit?: number;
  showCharacterCount?: boolean;
  animatedLabel?: boolean;
  focusRing?: boolean;
  autoResize?: boolean;
  minRows?: number;
  maxRows?: number;
}

// Validation icon component
const ValidationIcon: React.FC<{ type: ValidationState['type']; animated?: boolean }> = ({ 
  type, 
  animated = true 
}) => {
  const iconClass = cn("w-4 h-4", animated && "transition-all duration-200");
  
  switch (type) {
    case 'success':
      return <Check className={cn(iconClass, "text-green-600")} />;
    case 'error':
      return <X className={cn(iconClass, "text-red-600")} />;
    case 'warning':
      return <AlertTriangle className={cn(iconClass, "text-yellow-600")} />;
    case 'info':
      return <Info className={cn(iconClass, "text-blue-600")} />;
    default:
      return null;
  }
};

// Interactive Input Component
export const InteractiveInput: React.FC<InteractiveInputProps> = ({
  label,
  description,
  validation,
  showValidation = true,
  realTimeValidation = false,
  onValidationChange,
  leftIcon,
  rightIcon,
  showPasswordToggle = false,
  characterLimit,
  showCharacterCount = false,
  animatedLabel = true,
  focusRing = true,
  successMessage,
  errorMessage,
  className,
  type = 'text',
  value,
  onChange,
  onBlur,
  onFocus,
  ...props
}) => {
  const [isFocused, setIsFocused] = React.useState(false);
  const [showPassword, setShowPassword] = React.useState(false);
  const [internalValue, setInternalValue] = React.useState(value || '');
  const [hasInteracted, setHasInteracted] = React.useState(false);

  const inputRef = React.useRef<HTMLInputElement>(null);
  const inputId = React.useId();

  // Update internal value when prop changes
  React.useEffect(() => {
    setInternalValue(value || '');
  }, [value]);

  // Real-time validation
  React.useEffect(() => {
    if (realTimeValidation && hasInteracted && onValidationChange) {
      // Basic validation examples
      const currentValue = String(internalValue);
      let validationState: ValidationState = { isValid: true };

      if (type === 'email' && currentValue) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        validationState = {
          isValid: emailRegex.test(currentValue),
          message: emailRegex.test(currentValue) ? successMessage : errorMessage || 'Please enter a valid email address',
          type: emailRegex.test(currentValue) ? 'success' : 'error',
        };
      } else if (characterLimit && currentValue.length > characterLimit) {
        validationState = {
          isValid: false,
          message: `Character limit exceeded (${currentValue.length}/${characterLimit})`,
          type: 'error',
        };
      } else if (currentValue && successMessage) {
        validationState = {
          isValid: true,
          message: successMessage,
          type: 'success',
        };
      }

      onValidationChange(validationState);
    }
  }, [internalValue, realTimeValidation, hasInteracted, type, characterLimit, successMessage, errorMessage, onValidationChange]);

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    setHasInteracted(true);
    onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    setHasInteracted(true);
    onChange?.(e);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const currentType = type === 'password' && showPassword ? 'text' : type;
  const hasError = validation && !validation.isValid && validation.type === 'error';
  const hasSuccess = validation && validation.isValid && validation.type === 'success';
  const characterCount = String(internalValue).length;
  const isOverLimit = characterLimit && characterCount > characterLimit;

  return (
    <div className="space-y-2">
      {/* Label */}
      {label && (
        <Label
          htmlFor={inputId}
          className={cn(
            "text-sm font-medium transition-all duration-200",
            animatedLabel && isFocused && "text-blue-600",
            hasError && "text-red-600",
            hasSuccess && "text-green-600"
          )}
        >
          {label}
        </Label>
      )}

      {/* Description */}
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}

      {/* Input Container */}
      <div className="relative">
        {/* Left Icon */}
        {leftIcon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
            {leftIcon}
          </div>
        )}

        {/* Input */}
        <Input
          ref={inputRef}
          id={inputId}
          type={currentType}
          value={internalValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className={cn(
            "transition-all duration-200",
            leftIcon && "pl-10",
            (rightIcon || showPasswordToggle || (validation && showValidation)) && "pr-10",
            focusRing && isFocused && "ring-2 ring-blue-500/20",
            hasError && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
            hasSuccess && "border-green-500 focus:border-green-500 focus:ring-green-500/20",
            isOverLimit && "border-red-500",
            className
          )}
          {...props}
        />

        {/* Right Icons */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          {/* Validation Icon */}
          {validation && showValidation && (
            <ValidationIcon type={validation.type} />
          )}

          {/* Password Toggle */}
          {showPasswordToggle && type === 'password' && (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="text-muted-foreground hover:text-foreground transition-colors"
              tabIndex={-1}
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          )}

          {/* Custom Right Icon */}
          {rightIcon && (
            <div className="text-muted-foreground">
              {rightIcon}
            </div>
          )}
        </div>
      </div>

      {/* Character Count */}
      {showCharacterCount && characterLimit && (
        <div className="flex justify-end">
          <span className={cn(
            "text-xs transition-colors",
            isOverLimit ? "text-red-600" : "text-muted-foreground"
          )}>
            {characterCount}/{characterLimit}
          </span>
        </div>
      )}

      {/* Validation Message */}
      {validation && validation.message && showValidation && (
        <div className={cn(
          "flex items-center space-x-2 text-sm transition-all duration-200",
          validation.type === 'error' && "text-red-600",
          validation.type === 'success' && "text-green-600",
          validation.type === 'warning' && "text-yellow-600",
          validation.type === 'info' && "text-blue-600"
        )}>
          <ValidationIcon type={validation.type} />
          <span>{validation.message}</span>
        </div>
      )}
    </div>
  );
};

// Interactive Textarea Component
export const InteractiveTextarea: React.FC<InteractiveTextareaProps> = ({
  label,
  description,
  validation,
  showValidation = true,
  realTimeValidation = false,
  onValidationChange,
  characterLimit,
  showCharacterCount = false,
  animatedLabel = true,
  focusRing = true,
  autoResize = false,
  minRows = 3,
  maxRows = 10,
  className,
  value,
  onChange,
  onBlur,
  onFocus,
  ...props
}) => {
  const [isFocused, setIsFocused] = React.useState(false);
  const [internalValue, setInternalValue] = React.useState(value || '');
  const [hasInteracted, setHasInteracted] = React.useState(false);

  const textareaRef = React.useRef<HTMLTextAreaElement>(null);
  const textareaId = React.useId();

  // Update internal value when prop changes
  React.useEffect(() => {
    setInternalValue(value || '');
  }, [value]);

  // Auto-resize functionality
  React.useEffect(() => {
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
      const minHeight = lineHeight * minRows;
      const maxHeight = lineHeight * maxRows;
      
      textarea.style.height = `${Math.min(Math.max(scrollHeight, minHeight), maxHeight)}px`;
    }
  }, [internalValue, autoResize, minRows, maxRows]);

  // Real-time validation
  React.useEffect(() => {
    if (realTimeValidation && hasInteracted && onValidationChange) {
      const currentValue = String(internalValue);
      let validationState: ValidationState = { isValid: true };

      if (characterLimit && currentValue.length > characterLimit) {
        validationState = {
          isValid: false,
          message: `Character limit exceeded (${currentValue.length}/${characterLimit})`,
          type: 'error',
        };
      }

      onValidationChange(validationState);
    }
  }, [internalValue, realTimeValidation, hasInteracted, characterLimit, onValidationChange]);

  const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setIsFocused(true);
    setHasInteracted(true);
    onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    setHasInteracted(true);
    onChange?.(e);
  };

  const hasError = validation && !validation.isValid && validation.type === 'error';
  const hasSuccess = validation && validation.isValid && validation.type === 'success';
  const characterCount = String(internalValue).length;
  const isOverLimit = characterLimit && characterCount > characterLimit;

  return (
    <div className="space-y-2">
      {/* Label */}
      {label && (
        <Label
          htmlFor={textareaId}
          className={cn(
            "text-sm font-medium transition-all duration-200",
            animatedLabel && isFocused && "text-blue-600",
            hasError && "text-red-600",
            hasSuccess && "text-green-600"
          )}
        >
          {label}
        </Label>
      )}

      {/* Description */}
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}

      {/* Textarea */}
      <Textarea
        ref={textareaRef}
        id={textareaId}
        value={internalValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={cn(
          "transition-all duration-200 resize-none",
          focusRing && isFocused && "ring-2 ring-blue-500/20",
          hasError && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
          hasSuccess && "border-green-500 focus:border-green-500 focus:ring-green-500/20",
          isOverLimit && "border-red-500",
          autoResize && "overflow-hidden",
          className
        )}
        rows={autoResize ? minRows : undefined}
        {...props}
      />

      {/* Character Count */}
      {showCharacterCount && characterLimit && (
        <div className="flex justify-end">
          <span className={cn(
            "text-xs transition-colors",
            isOverLimit ? "text-red-600" : "text-muted-foreground"
          )}>
            {characterCount}/{characterLimit}
          </span>
        </div>
      )}

      {/* Validation Message */}
      {validation && validation.message && showValidation && (
        <div className={cn(
          "flex items-center space-x-2 text-sm transition-all duration-200",
          validation.type === 'error' && "text-red-600",
          validation.type === 'success' && "text-green-600",
          validation.type === 'warning' && "text-yellow-600",
          validation.type === 'info' && "text-blue-600"
        )}>
          <ValidationIcon type={validation.type} />
          <span>{validation.message}</span>
        </div>
      )}
    </div>
  );
};

// Hover Feedback Component
export interface HoverFeedbackProps {
  children: React.ReactNode;
  hoverEffect?: 'scale' | 'lift' | 'glow' | 'border' | 'background' | 'none';
  hoverColor?: string;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
}

export const HoverFeedback: React.FC<HoverFeedbackProps> = ({
  children,
  hoverEffect = 'scale',
  hoverColor,
  disabled = false,
  className,
  onClick,
}) => {
  const [isHovered, setIsHovered] = React.useState(false);

  const getHoverClasses = () => {
    if (disabled) return '';

    switch (hoverEffect) {
      case 'scale':
        return 'hover:scale-105 transition-transform duration-200';
      case 'lift':
        return 'hover:shadow-lg hover:-translate-y-1 transition-all duration-200';
      case 'glow':
        return 'hover:shadow-lg hover:shadow-blue-500/25 transition-shadow duration-200';
      case 'border':
        return 'hover:border-blue-500 transition-colors duration-200';
      case 'background':
        return 'hover:bg-accent transition-colors duration-200';
      default:
        return '';
    }
  };

  return (
    <div
      className={cn(
        "cursor-pointer",
        getHoverClasses(),
        disabled && "cursor-not-allowed opacity-50",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={disabled ? undefined : onClick}
      style={hoverColor && isHovered ? { color: hoverColor } : undefined}
    >
      {children}
    </div>
  );
};

// Focus Ring Component
export interface FocusRingProps {
  children: React.ReactNode;
  ringColor?: string;
  ringWidth?: number;
  ringOffset?: number;
  className?: string;
}

export const FocusRing: React.FC<FocusRingProps> = ({
  children,
  ringColor = 'ring-blue-500',
  ringWidth = 2,
  ringOffset = 2,
  className,
}) => {
  return (
    <div
      className={cn(
        "focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 rounded-md transition-all duration-200",
        `focus-within:${ringColor}`,
        `focus-within:ring-${ringWidth}`,
        `focus-within:ring-offset-${ringOffset}`,
        className
      )}
    >
      {children}
    </div>
  );
};

export default { InteractiveInput, InteractiveTextarea, HoverFeedback, FocusRing };
