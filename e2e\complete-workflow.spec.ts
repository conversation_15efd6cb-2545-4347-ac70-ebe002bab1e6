import { test, expect } from '@playwright/test';

test.describe('Complete User Workflow E2E', () => {
  test('should complete full workflow: auth → code input → reactor → GitHub PR', async ({ page }) => {
    // Step 1: Start unauthenticated
    await page.goto('/');
    
    // Should show landing page or login prompt
    await expect(page.locator('text=Sign in')).toBeVisible({ timeout: 5000 });
    
    // Step 2: Authenticate with Supabase
    await page.route('**/auth/v1/token**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock-access-token',
          user: {
            id: 'workflow-user-123',
            email: '<EMAIL>',
            name: 'Workflow Test User'
          }
        })
      });
    });

    await page.click('text=Sign in');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword');
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard
    await expect(page.locator('text=Dashboard')).toBeVisible({ timeout: 10000 });
    
    // Step 3: Connect GitHub
    await page.route('**/functions/v1/github-oauth', async route => {
      const method = route.request().method();
      
      if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            hasToken: false,
            tokenInfo: null
          })
        });
      } else if (method === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            user: {
              login: 'workflowuser',
              name: 'Workflow User',
              avatar_url: 'https://github.com/avatar.jpg'
            }
          })
        });
      }
    });

    // Initially should show "Connect GitHub"
    await expect(page.locator('text=Connect GitHub')).toBeVisible();
    
    await page.click('text=Connect GitHub');
    
    // Simulate OAuth success
    await page.evaluate(() => {
      window.postMessage({
        type: 'github-oauth-success',
        data: {
          login: 'workflowuser',
          name: 'Workflow User'
        }
      }, '*');
    });
    
    // Should show connected state
    await expect(page.locator('text=Connected')).toBeVisible();
    
    // Step 4: Input code for optimization
    const codeToOptimize = `
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10));
    `.trim();
    
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type(codeToOptimize);
    
    // Step 5: Run Reactor Loop
    await page.route('**/functions/v1/ai-loop', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          finalPatch: {
            operations: [
              {
                op: 'replace',
                path: '/function',
                value: `
function fibonacci(n, memo = {}) {
  if (n in memo) return memo[n];
  if (n <= 1) return n;
  memo[n] = fibonacci(n - 1, memo) + fibonacci(n - 2, memo);
  return memo[n];
}

console.log(fibonacci(10));
                `.trim()
              }
            ],
            description: 'Added memoization to optimize fibonacci function performance'
          },
          finalScore: 0.95,
          iterations: 3,
          logs: [
            {
              iteration: 1,
              plan: 'Analyze fibonacci function for optimization opportunities',
              critique: 'Function has exponential time complexity due to repeated calculations',
              score: 0.3
            },
            {
              iteration: 2,
              plan: 'Implement memoization to cache results',
              critique: 'Good approach, but can be improved with default parameter',
              score: 0.8
            },
            {
              iteration: 3,
              plan: 'Optimize memoization implementation',
              critique: 'Excellent implementation with clean default parameter',
              score: 0.95
            }
          ],
          completed: true
        })
      });
    });
    
    await page.click('text=Start');
    
    // Should show running state
    await expect(page.locator('text=Running')).toBeVisible();
    
    // Wait for completion
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 15000 });
    
    // Should show final score
    await expect(page.locator('text=Final score: 95.0%')).toBeVisible();
    
    // Step 6: Review diff and patch
    await expect(page.locator('.monaco-diff-editor')).toBeVisible();
    
    // Switch to patch view
    await page.click('text=Patch');
    await expect(page.locator('text=Added memoization')).toBeVisible();
    
    // Step 7: Create GitHub PR
    await page.route('**/functions/v1/create-pr', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          pr: {
            url: 'https://github.com/workflowuser/test-repo/pull/123',
            number: 123,
            branch: 'reactor/fibonacci-optimization',
            title: 'feat: optimize fibonacci with memoization'
          }
        })
      });
    });
    
    await page.route('**/api.github.com/user/repos', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 1,
            name: 'test-repo',
            full_name: 'workflowuser/test-repo',
            permissions: { admin: true, push: true, pull: true }
          }
        ])
      });
    });
    
    await page.click('text=Create PR');
    
    // Fill PR details
    await page.fill('input[placeholder*="title"]', 'feat: optimize fibonacci with memoization');
    await page.fill('textarea[placeholder*="description"]', 'Implemented memoization to improve performance from O(2^n) to O(n)');
    
    // Select repository
    await page.selectOption('select[name="repository"]', 'workflowuser/test-repo');
    
    // Create the PR
    await page.click('button:has-text("Create Pull Request")');
    
    // Should show success
    await expect(page.locator('text=Pull Request created successfully')).toBeVisible();
    await expect(page.locator('text=#123')).toBeVisible();
    
    // Should show link to GitHub PR
    const prLink = page.locator('a[href="https://github.com/workflowuser/test-repo/pull/123"]');
    await expect(prLink).toBeVisible();
    
    // Step 8: Verify workflow completion
    await expect(page.locator('text=Workflow completed successfully')).toBeVisible();
    
    // Should be able to start a new workflow
    await expect(page.locator('text=Start New Optimization')).toBeVisible();
  });

  test('should handle workflow interruption and recovery', async ({ page }) => {
    // Set up authenticated state
    await page.addInitScript(() => {
      const authToken = {
        access_token: 'valid-token',
        user: { id: 'test-user', email: '<EMAIL>' }
      };
      window.localStorage.setItem('supabase.auth.token', JSON.stringify(authToken));
    });

    await page.goto('/dashboard');
    
    // Start reactor loop
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.type('function test() { return "hello"; }');
    
    // Mock long-running reactor loop
    await page.route('**/functions/v1/ai-loop', async route => {
      // Simulate long delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          finalPatch: { operations: [] },
          completed: true
        })
      });
    });
    
    await page.click('text=Start');
    await expect(page.locator('text=Running')).toBeVisible();
    
    // Interrupt by stopping
    await page.click('text=Stop');
    
    // Should show stopped state
    await expect(page.locator('text=stopped by user')).toBeVisible();
    
    // Should be able to restart
    await page.click('text=Start');
    await expect(page.locator('text=Running')).toBeVisible();
    
    // Complete the workflow
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 10000 });
  });

  test('should persist workflow state across page refreshes', async ({ page }) => {
    // Set up authenticated state
    await page.addInitScript(() => {
      const authToken = {
        access_token: 'valid-token',
        user: { id: 'test-user', email: '<EMAIL>' }
      };
      window.localStorage.setItem('supabase.auth.token', JSON.stringify(authToken));
    });

    await page.goto('/dashboard');
    
    // Input some code
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('const message = "Hello, World!";');
    
    // Refresh the page
    await page.reload();
    
    // Code should be preserved
    await expect(page.locator('text=Hello, World!')).toBeVisible();
    
    // Should still be authenticated
    await expect(page.locator('text=Start')).toBeVisible();
  });

  test('should handle multiple concurrent workflows', async ({ page, context }) => {
    // Set up authenticated state
    await page.addInitScript(() => {
      const authToken = {
        access_token: 'valid-token',
        user: { id: 'test-user', email: '<EMAIL>' }
      };
      window.localStorage.setItem('supabase.auth.token', JSON.stringify(authToken));
    });

    // Open second tab
    const page2 = await context.newPage();
    await page2.addInitScript(() => {
      const authToken = {
        access_token: 'valid-token',
        user: { id: 'test-user', email: '<EMAIL>' }
      };
      window.localStorage.setItem('supabase.auth.token', JSON.stringify(authToken));
    });

    await page.goto('/dashboard');
    await page2.goto('/dashboard');
    
    // Start workflow in first tab
    const editor1 = page.locator('.monaco-editor');
    await editor1.click();
    await page.keyboard.type('function test1() {}');
    
    // Start workflow in second tab
    const editor2 = page2.locator('.monaco-editor');
    await editor2.click();
    await page2.keyboard.type('function test2() {}');
    
    // Both should be able to run independently
    await page.click('text=Start');
    await page2.click('text=Start');
    
    await expect(page.locator('text=Running')).toBeVisible();
    await expect(page2.locator('text=Running')).toBeVisible();
  });
});
