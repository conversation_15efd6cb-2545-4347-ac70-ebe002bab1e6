import React from 'react';
import { cn } from '@/lib/utils';
import { useResponsive } from '@/hooks/useResponsive';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ResponsiveGrid, 
  ResponsiveGridItem, 
  ResponsiveFlex, 
  ResponsiveContainer,
  ResponsiveVisibility 
} from '@/components/ui/responsive-grid';
import { Monitor, Smartphone, Tablet, Laptop, RotateCcw } from 'lucide-react';

// Responsive debugger component for testing layouts
export const ResponsiveDebugger: React.FC<{ 
  show?: boolean; 
  onClose?: () => void;
}> = ({ show = false, onClose }) => {
  const screenInfo = useResponsive();

  if (!show) return null;

  const getDeviceIcon = () => {
    switch (screenInfo.deviceType) {
      case 'mobile': return <Smartphone className="w-4 h-4" />;
      case 'tablet': return <Tablet className="w-4 h-4" />;
      case 'desktop': return <Monitor className="w-4 h-4" />;
      default: return <Laptop className="w-4 h-4" />;
    }
  };

  const getBreakpointColor = (breakpoint: string) => {
    switch (breakpoint) {
      case 'xs': return 'bg-red-100 text-red-800 border-red-200';
      case 'sm': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'md': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'lg': return 'bg-green-100 text-green-800 border-green-200';
      case 'xl': return 'bg-blue-100 text-blue-800 border-blue-200';
      case '2xl': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-background rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-auto">
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Monitor className="w-5 h-5" />
              <h2 className="text-lg font-semibold">Responsive Layout Debugger</h2>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Current Screen Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {getDeviceIcon()}
                <span>Current Screen Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{screenInfo.width}px</div>
                  <div className="text-sm text-muted-foreground">Width</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{screenInfo.height}px</div>
                  <div className="text-sm text-muted-foreground">Height</div>
                </div>
                <div className="text-center">
                  <Badge className={getBreakpointColor(screenInfo.breakpoint)}>
                    {screenInfo.breakpoint.toUpperCase()}
                  </Badge>
                  <div className="text-sm text-muted-foreground mt-1">Breakpoint</div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1">
                    {getDeviceIcon()}
                    <span className="text-sm font-medium">{screenInfo.deviceType}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">Device Type</div>
                </div>
              </div>

              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-lg font-semibold">
                    {screenInfo.orientation === 'landscape' ? <RotateCcw className="w-5 h-5 mx-auto" /> : '📱'}
                  </div>
                  <div className="text-sm text-muted-foreground">{screenInfo.orientation}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold">{screenInfo.isTouch ? '👆' : '🖱️'}</div>
                  <div className="text-sm text-muted-foreground">{screenInfo.isTouch ? 'Touch' : 'Mouse'}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold">{screenInfo.pixelRatio}x</div>
                  <div className="text-sm text-muted-foreground">Pixel Ratio</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold">
                    {screenInfo.isMobile ? '📱' : screenInfo.isTablet ? '📱' : '💻'}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {screenInfo.isMobile ? 'Mobile' : screenInfo.isTablet ? 'Tablet' : 'Desktop'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Breakpoint Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Breakpoint Visibility Tests</CardTitle>
              <CardDescription>
                These elements show/hide based on different breakpoints
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <ResponsiveVisibility show={{ xs: true }} hide={{ sm: true }}>
                  <Badge variant="destructive">XS Only (0-639px)</Badge>
                </ResponsiveVisibility>

                <ResponsiveVisibility show={{ sm: true }} hide={{ md: true }}>
                  <Badge variant="secondary">SM Only (640-767px)</Badge>
                </ResponsiveVisibility>

                <ResponsiveVisibility show={{ md: true }} hide={{ lg: true }}>
                  <Badge variant="outline">MD Only (768-1023px)</Badge>
                </ResponsiveVisibility>

                <ResponsiveVisibility show={{ lg: true }} hide={{ xl: true }}>
                  <Badge variant="default">LG Only (1024-1279px)</Badge>
                </ResponsiveVisibility>

                <ResponsiveVisibility show={{ xl: true }} hide={{ '2xl': true }}>
                  <Badge className="bg-blue-600 text-white">XL Only (1280-1535px)</Badge>
                </ResponsiveVisibility>

                <ResponsiveVisibility show={{ '2xl': true }}>
                  <Badge className="bg-purple-600 text-white">2XL+ (1536px+)</Badge>
                </ResponsiveVisibility>
              </div>
            </CardContent>
          </Card>

          {/* Grid Layout Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Responsive Grid Layout</CardTitle>
              <CardDescription>
                Grid adapts from 1 column on mobile to 4 columns on desktop
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveGrid
                cols={{ xs: 1, sm: 2, md: 3, lg: 4 }}
                gap={{ xs: 2, sm: 4, md: 6 }}
              >
                {Array.from({ length: 8 }).map((_, i) => (
                  <ResponsiveGridItem key={i}>
                    <div className="bg-muted p-4 rounded-lg text-center">
                      <div className="font-semibold">Item {i + 1}</div>
                      <div className="text-sm text-muted-foreground">
                        {screenInfo.breakpoint} breakpoint
                      </div>
                    </div>
                  </ResponsiveGridItem>
                ))}
              </ResponsiveGrid>
            </CardContent>
          </Card>

          {/* Flex Layout Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Responsive Flex Layout</CardTitle>
              <CardDescription>
                Flex direction changes from column on mobile to row on desktop
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveFlex
                direction={{ xs: 'col', md: 'row' }}
                gap={{ xs: 2, md: 4 }}
                justify={{ xs: 'start', md: 'between' }}
                align={{ xs: 'stretch', md: 'center' }}
              >
                <div className="bg-blue-100 p-4 rounded-lg flex-1">
                  <div className="font-semibold">Flex Item 1</div>
                  <div className="text-sm text-muted-foreground">
                    Direction: {screenInfo.width < 768 ? 'column' : 'row'}
                  </div>
                </div>
                <div className="bg-green-100 p-4 rounded-lg flex-1">
                  <div className="font-semibold">Flex Item 2</div>
                  <div className="text-sm text-muted-foreground">
                    Responsive flex layout
                  </div>
                </div>
                <div className="bg-yellow-100 p-4 rounded-lg flex-1">
                  <div className="font-semibold">Flex Item 3</div>
                  <div className="text-sm text-muted-foreground">
                    Adapts to screen size
                  </div>
                </div>
              </ResponsiveFlex>
            </CardContent>
          </Card>

          {/* Container Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Responsive Container</CardTitle>
              <CardDescription>
                Container max-width and padding adapt to screen size
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer
                maxWidth={{ xs: 'full', sm: 'sm', md: 'md', lg: 'lg' }}
                padding={{ xs: 2, sm: 4, md: 6, lg: 8 }}
                className="bg-muted rounded-lg"
              >
                <div className="text-center">
                  <div className="font-semibold">Responsive Container</div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Max width: {screenInfo.breakpoint === 'xs' ? 'full' : screenInfo.breakpoint}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Padding adapts to screen size
                  </div>
                </div>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Typography Scale Test */}
          <Card>
            <CardHeader>
              <CardTitle>Responsive Typography</CardTitle>
              <CardDescription>
                Text sizes adapt to screen size for better readability
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold">
                  Responsive Heading
                </h1>
                <p className="text-sm sm:text-base md:text-lg text-muted-foreground">
                  This paragraph text scales with the screen size. On mobile devices, 
                  it uses smaller text for better readability, while on larger screens 
                  it increases in size for improved visual hierarchy.
                </p>
                <div className="text-xs sm:text-sm md:text-base lg:text-lg">
                  Current size: {screenInfo.breakpoint} breakpoint
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// Hook to toggle responsive debugger
export const useResponsiveDebugger = () => {
  const [isOpen, setIsOpen] = React.useState(false);

  const toggle = React.useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const open = React.useCallback(() => {
    setIsOpen(true);
  }, []);

  const close = React.useCallback(() => {
    setIsOpen(false);
  }, []);

  return {
    isOpen,
    toggle,
    open,
    close,
    ResponsiveDebugger: React.useCallback((props: any) => (
      <ResponsiveDebugger show={isOpen} onClose={close} {...props} />
    ), [isOpen, close]),
  };
};

export default ResponsiveDebugger;
