import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types
export interface AIProviderConfig {
  type: 'openai' | 'anthropic' | 'google' | 'azure';
  model: string;
  temperature: number;
  maxTokens: number;
  apiKey?: string;
  endpoint?: string;
  enabled: boolean;
}

export interface CostGuardConfig {
  enabled: boolean;
  maxCostPerLoop: number;
  maxCostPerHour: number;
  maxCostPerDay: number;
  alertThresholds: {
    nearLimitWarning: number; // percentage
    costPerHour: number;
  };
}

export interface NotificationSettings {
  email: {
    enabled: boolean;
    transformationComplete: boolean;
    transformationFailed: boolean;
    costAlerts: boolean;
    systemAlerts: boolean;
  };
  browser: {
    enabled: boolean;
    transformationComplete: boolean;
    transformationFailed: boolean;
    costAlerts: boolean;
    systemAlerts: boolean;
  };
  sound: {
    enabled: boolean;
    volume: number;
    transformationComplete: boolean;
    transformationFailed: boolean;
  };
}

export interface SecuritySettings {
  twoFactorEnabled: boolean;
  sessionTimeout: number; // minutes
  allowedIPs: string[];
  apiKeyRotationDays: number;
  encryptLocalStorage: boolean;
}

export interface AdvancedSettings {
  debugMode: boolean;
  verboseLogging: boolean;
  experimentalFeatures: boolean;
  telemetryLevel: 'none' | 'basic' | 'full';
  autoBackup: boolean;
  backupRetentionDays: number;
  maxConcurrentTransformations: number;
  requestTimeout: number; // seconds
}

export interface SettingsState {
  // AI Provider Settings
  plannerProvider: AIProviderConfig;
  criticProvider: AIProviderConfig;
  fallbackProviders: AIProviderConfig[];
  
  // Default Transformation Settings
  defaultMaxIterations: number;
  defaultScoreThreshold: number;
  autoCreatePR: boolean;
  githubRepo: {
    owner: string;
    name: string;
    branch: string;
  } | null;
  
  // Cost Management
  costGuard: CostGuardConfig;
  
  // Notifications
  notifications: NotificationSettings;
  
  // Security
  security: SecuritySettings;
  
  // Advanced
  advanced: AdvancedSettings;
  
  // UI Preferences
  ui: {
    compactMode: boolean;
    showAdvancedOptions: boolean;
    defaultView: 'dashboard' | 'monitoring';
    autoRefreshInterval: number; // seconds
    maxLogLines: number;
    showLineNumbers: boolean;
    enableAnimations: boolean;
  };
  
  // Keyboard Shortcuts
  shortcuts: Record<string, string>;
  
  // Last updated timestamp
  lastUpdated: Date;
}

export interface SettingsActions {
  // AI Provider actions
  updatePlannerProvider: (config: Partial<AIProviderConfig>) => void;
  updateCriticProvider: (config: Partial<AIProviderConfig>) => void;
  addFallbackProvider: (config: AIProviderConfig) => void;
  removeFallbackProvider: (index: number) => void;
  updateFallbackProvider: (index: number, config: Partial<AIProviderConfig>) => void;
  
  // Transformation settings
  setDefaultMaxIterations: (iterations: number) => void;
  setDefaultScoreThreshold: (threshold: number) => void;
  setAutoCreatePR: (enabled: boolean) => void;
  setGitHubRepo: (repo: SettingsState['githubRepo']) => void;
  
  // Cost guard actions
  updateCostGuard: (config: Partial<CostGuardConfig>) => void;
  
  // Notification actions
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => void;
  
  // Security actions
  updateSecuritySettings: (settings: Partial<SecuritySettings>) => void;
  
  // Advanced actions
  updateAdvancedSettings: (settings: Partial<AdvancedSettings>) => void;
  
  // UI actions
  updateUISettings: (settings: Partial<SettingsState['ui']>) => void;
  
  // Shortcut actions
  updateShortcut: (action: string, shortcut: string) => void;
  resetShortcuts: () => void;
  
  // Utility actions
  resetToDefaults: () => void;
  exportSettings: () => SettingsState;
  importSettings: (settings: Partial<SettingsState>) => void;
  validateSettings: () => { valid: boolean; errors: string[] };
}

// Default shortcuts
const defaultShortcuts = {
  'run-transformation': 'Ctrl+Enter',
  'stop-transformation': 'Ctrl+Shift+S',
  'toggle-sidebar': 'Ctrl+B',
  'toggle-examples': 'Ctrl+E',
  'toggle-help': 'Ctrl+H',
  'open-monitoring': 'Ctrl+M',
  'open-settings': 'Ctrl+,',
  'command-palette': 'Ctrl+K',
  'focus-prompt': 'Ctrl+1',
  'focus-diff': 'Ctrl+2',
  'focus-logs': 'Ctrl+3',
  'download-code': 'Ctrl+D',
  'create-pr': 'Ctrl+Shift+P',
};

// Initial state
const initialState: SettingsState = {
  plannerProvider: {
    type: 'openai',
    model: 'gpt-4-turbo',
    temperature: 0.1,
    maxTokens: 4000,
    enabled: true,
  },
  
  criticProvider: {
    type: 'anthropic',
    model: 'claude-3-sonnet-20240229',
    temperature: 0.2,
    maxTokens: 4000,
    enabled: true,
  },
  
  fallbackProviders: [
    {
      type: 'google',
      model: 'gemini-pro',
      temperature: 0.1,
      maxTokens: 4000,
      enabled: false,
    },
  ],
  
  defaultMaxIterations: 10,
  defaultScoreThreshold: 0.8,
  autoCreatePR: false,
  githubRepo: null,
  
  costGuard: {
    enabled: true,
    maxCostPerLoop: 5.0,
    maxCostPerHour: 20.0,
    maxCostPerDay: 100.0,
    alertThresholds: {
      nearLimitWarning: 80,
      costPerHour: 15.0,
    },
  },
  
  notifications: {
    email: {
      enabled: false,
      transformationComplete: true,
      transformationFailed: true,
      costAlerts: true,
      systemAlerts: true,
    },
    browser: {
      enabled: true,
      transformationComplete: true,
      transformationFailed: true,
      costAlerts: true,
      systemAlerts: true,
    },
    sound: {
      enabled: true,
      volume: 0.5,
      transformationComplete: true,
      transformationFailed: true,
    },
  },
  
  security: {
    twoFactorEnabled: false,
    sessionTimeout: 480, // 8 hours
    allowedIPs: [],
    apiKeyRotationDays: 90,
    encryptLocalStorage: false,
  },
  
  advanced: {
    debugMode: false,
    verboseLogging: false,
    experimentalFeatures: false,
    telemetryLevel: 'basic',
    autoBackup: true,
    backupRetentionDays: 30,
    maxConcurrentTransformations: 1,
    requestTimeout: 30,
  },
  
  ui: {
    compactMode: false,
    showAdvancedOptions: false,
    defaultView: 'dashboard',
    autoRefreshInterval: 30,
    maxLogLines: 1000,
    showLineNumbers: true,
    enableAnimations: true,
  },
  
  shortcuts: defaultShortcuts,
  
  lastUpdated: new Date(),
};

// Create the store
export const useSettingsStore = create<SettingsState & SettingsActions>()(
  persist(
    immer((set, get) => ({
      ...initialState,
      
      // AI Provider actions
      updatePlannerProvider: (config) => set((state) => {
        Object.assign(state.plannerProvider, config);
        state.lastUpdated = new Date();
      }),
      
      updateCriticProvider: (config) => set((state) => {
        Object.assign(state.criticProvider, config);
        state.lastUpdated = new Date();
      }),
      
      addFallbackProvider: (config) => set((state) => {
        state.fallbackProviders.push(config);
        state.lastUpdated = new Date();
      }),
      
      removeFallbackProvider: (index) => set((state) => {
        if (index >= 0 && index < state.fallbackProviders.length) {
          state.fallbackProviders.splice(index, 1);
          state.lastUpdated = new Date();
        }
      }),
      
      updateFallbackProvider: (index, config) => set((state) => {
        if (index >= 0 && index < state.fallbackProviders.length) {
          Object.assign(state.fallbackProviders[index], config);
          state.lastUpdated = new Date();
        }
      }),
      
      // Transformation settings
      setDefaultMaxIterations: (iterations) => set((state) => {
        state.defaultMaxIterations = Math.max(1, Math.min(50, iterations));
        state.lastUpdated = new Date();
      }),
      
      setDefaultScoreThreshold: (threshold) => set((state) => {
        state.defaultScoreThreshold = Math.max(0.1, Math.min(1.0, threshold));
        state.lastUpdated = new Date();
      }),
      
      setAutoCreatePR: (enabled) => set((state) => {
        state.autoCreatePR = enabled;
        state.lastUpdated = new Date();
      }),
      
      setGitHubRepo: (repo) => set((state) => {
        state.githubRepo = repo;
        state.lastUpdated = new Date();
      }),
      
      // Cost guard actions
      updateCostGuard: (config) => set((state) => {
        Object.assign(state.costGuard, config);
        state.lastUpdated = new Date();
      }),
      
      // Notification actions
      updateNotificationSettings: (settings) => set((state) => {
        Object.assign(state.notifications, settings);
        state.lastUpdated = new Date();
      }),
      
      // Security actions
      updateSecuritySettings: (settings) => set((state) => {
        Object.assign(state.security, settings);
        state.lastUpdated = new Date();
      }),
      
      // Advanced actions
      updateAdvancedSettings: (settings) => set((state) => {
        Object.assign(state.advanced, settings);
        state.lastUpdated = new Date();
      }),
      
      // UI actions
      updateUISettings: (settings) => set((state) => {
        Object.assign(state.ui, settings);
        state.lastUpdated = new Date();
      }),
      
      // Shortcut actions
      updateShortcut: (action, shortcut) => set((state) => {
        state.shortcuts[action] = shortcut;
        state.lastUpdated = new Date();
      }),
      
      resetShortcuts: () => set((state) => {
        state.shortcuts = { ...defaultShortcuts };
        state.lastUpdated = new Date();
      }),
      
      // Utility actions
      resetToDefaults: () => set(() => ({
        ...initialState,
        lastUpdated: new Date(),
      })),
      
      exportSettings: () => get(),
      
      importSettings: (settings) => set((state) => {
        Object.assign(state, settings);
        state.lastUpdated = new Date();
      }),
      
      validateSettings: () => {
        const state = get();
        const errors: string[] = [];
        
        // Validate AI provider settings
        if (!state.plannerProvider.model) {
          errors.push('Planner model is required');
        }
        if (!state.criticProvider.model) {
          errors.push('Critic model is required');
        }
        
        // Validate cost limits
        if (state.costGuard.maxCostPerLoop <= 0) {
          errors.push('Max cost per loop must be positive');
        }
        if (state.costGuard.maxCostPerHour <= 0) {
          errors.push('Max cost per hour must be positive');
        }
        if (state.costGuard.maxCostPerDay <= 0) {
          errors.push('Max cost per day must be positive');
        }
        
        // Validate transformation settings
        if (state.defaultMaxIterations < 1 || state.defaultMaxIterations > 50) {
          errors.push('Max iterations must be between 1 and 50');
        }
        if (state.defaultScoreThreshold < 0.1 || state.defaultScoreThreshold > 1.0) {
          errors.push('Score threshold must be between 0.1 and 1.0');
        }
        
        return {
          valid: errors.length === 0,
          errors,
        };
      },
    })),
    {
      name: 'metamorphic-reactor-settings',
      storage: createJSONStorage(() => localStorage),
      // Exclude sensitive data from persistence
      partialize: (state) => ({
        ...state,
        plannerProvider: {
          ...state.plannerProvider,
          apiKey: undefined, // Don't persist API keys
        },
        criticProvider: {
          ...state.criticProvider,
          apiKey: undefined,
        },
        fallbackProviders: state.fallbackProviders.map(provider => ({
          ...provider,
          apiKey: undefined,
        })),
      }),
    }
  )
);

// Selectors
export const selectAIProviders = (state: SettingsState & SettingsActions) => ({
  planner: state.plannerProvider,
  critic: state.criticProvider,
  fallbacks: state.fallbackProviders,
});

export const selectCostGuard = (state: SettingsState & SettingsActions) => state.costGuard;
export const selectNotifications = (state: SettingsState & SettingsActions) => state.notifications;
export const selectShortcuts = (state: SettingsState & SettingsActions) => state.shortcuts;
