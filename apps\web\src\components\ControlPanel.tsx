
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { HStack } from "@/components/ui/layout";
import { PlayIcon, StopIcon } from "@/components/ui/icon";
import { RotateCcw } from "lucide-react";

interface ControlPanelProps {
  isRunning: boolean;
  isLoading?: boolean;
  onRunLoop: () => void;
  onStop: () => void;
  onClear?: () => void;
}

export const ControlPanel = ({ isRunning, isLoading = false, onRunLoop, onStop, onClear }: ControlPanelProps) => {
  return (
    <HStack spacing="sm" align="center">
      {isRunning ? (
        <>
          <Button
            onClick={onStop}
            variant="destructive"
            size="sm"
            className="focus-ring transition-colors"
          >
            <StopIcon className="mr-2" aria-hidden="true" />
            Stop
          </Button>
          <Badge className="bg-status-running/20 text-status-running border-status-running/30 animate-pulse">
            <HStack spacing="xs" align="center">
              <div className="w-2 h-2 bg-status-running rounded-full animate-pulse-glow"></div>
              <span>Running</span>
            </HStack>
          </Badge>
        </>
      ) : (
        <>
          <Button
            onClick={onRunLoop}
            className="bg-gradient-to-r from-agent-planner to-agent-critic hover:opacity-90 text-white focus-ring transition-all"
            size="sm"
          >
            <PlayIcon className="mr-2" aria-hidden="true" />
            Run Loop
          </Button>
          {onClear && (
            <Button
              onClick={onClear}
              variant="outline"
              size="sm"
              className="focus-ring transition-colors"
              aria-label="Clear textarea"
            >
              <RotateCcw className="w-4 h-4 mr-2" aria-hidden="true" />
              Clear
            </Button>
          )}
        </>
      )}
    </HStack>
  );
};
