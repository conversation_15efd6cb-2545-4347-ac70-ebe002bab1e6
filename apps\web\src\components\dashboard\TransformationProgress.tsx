import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Square, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Zap,
  Brain,
  Eye,
  Code,
  GitBranch,
  DollarSign,
  Timer,
  Target
} from 'lucide-react';
import { useTransformationRealtime } from '@/hooks/useRealtimeData';
import { formatDistanceToNow } from 'date-fns';

interface TransformationProgressProps {
  transformationId: string | null;
  onStop?: (id: string) => void;
  onViewDetails?: (id: string) => void;
  compact?: boolean;
}

export const TransformationProgress: React.FC<TransformationProgressProps> = ({
  transformationId,
  onStop,
  onViewDetails,
  compact = false
}) => {
  const { 
    transformations, 
    agentLogs, 
    isLoading, 
    connectionStatus 
  } = useTransformationRealtime(transformationId);

  const [currentIteration, setCurrentIteration] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null);

  const transformation = transformations.find(t => t.id === transformationId);
  const sortedLogs = agentLogs.sort((a, b) => a.iteration - b.iteration);
  const latestLog = sortedLogs[sortedLogs.length - 1];

  useEffect(() => {
    if (transformation && sortedLogs.length > 0) {
      setCurrentIteration(transformation.iterations_count);
      
      // Calculate estimated time remaining
      if (transformation.status === 'running' && transformation.created_at) {
        const startTime = new Date(transformation.created_at).getTime();
        const currentTime = Date.now();
        const elapsedTime = currentTime - startTime;
        const avgTimePerIteration = elapsedTime / Math.max(transformation.iterations_count, 1);
        const remainingIterations = transformation.max_iterations - transformation.iterations_count;
        const estimatedRemaining = avgTimePerIteration * remainingIterations;
        setEstimatedTimeRemaining(estimatedRemaining);
      }
    }
  }, [transformation, sortedLogs]);

  if (!transformationId || !transformation) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5" />
            <span>Transformation Progress</span>
          </CardTitle>
          <CardDescription>
            No active transformation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Play className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Start a transformation to see progress here
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = () => {
    switch (transformation.status) {
      case 'running': return <Play className="w-4 h-4 text-blue-600 animate-pulse" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed': return <XCircle className="w-4 h-4 text-red-600" />;
      case 'timeout': return <Clock className="w-4 h-4 text-yellow-600" />;
      default: return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch (transformation.status) {
      case 'running': return 'text-blue-600';
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'timeout': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const progressPercentage = (transformation.iterations_count / transformation.max_iterations) * 100;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3,
    }).format(amount);
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  if (compact) {
    return (
      <div className="p-4 border rounded-lg bg-card">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className="text-sm font-medium">
              Iteration {transformation.iterations_count}/{transformation.max_iterations}
            </span>
          </div>
          <Badge variant={transformation.status === 'running' ? 'default' : 'secondary'}>
            {transformation.status}
          </Badge>
        </div>
        
        <Progress value={progressPercentage} className="h-2 mb-2" />
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{progressPercentage.toFixed(1)}% complete</span>
          {estimatedTimeRemaining && transformation.status === 'running' && (
            <span>~{formatDuration(estimatedTimeRemaining)} remaining</span>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Zap className="w-5 h-5" />
            <CardTitle>Transformation Progress</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={transformation.status === 'running' ? 'default' : 'secondary'}>
              {getStatusIcon()}
              <span className="ml-1">{transformation.status}</span>
            </Badge>
            {transformation.status === 'running' && onStop && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onStop(transformation.id)}
              >
                <Square className="w-3 h-3 mr-1" />
                Stop
              </Button>
            )}
          </div>
        </div>
        <CardDescription>
          Real-time progress tracking for transformation {transformation.id.slice(-8)}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Overview */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-sm text-muted-foreground">
              {transformation.iterations_count}/{transformation.max_iterations} iterations
            </span>
          </div>
          
          <Progress value={progressPercentage} className="h-3" />
          
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>{progressPercentage.toFixed(1)}% complete</span>
            {estimatedTimeRemaining && transformation.status === 'running' && (
              <span>~{formatDuration(estimatedTimeRemaining)} remaining</span>
            )}
          </div>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Target className="w-3 h-3 text-blue-600" />
              <span className="text-xs font-medium">Score</span>
            </div>
            <div className="text-lg font-bold">
              {latestLog?.score ? latestLog.score.toFixed(1) : '--'}
            </div>
            <div className="text-xs text-muted-foreground">
              Target: {(transformation.score_threshold * 100).toFixed(0)}
            </div>
          </div>

          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <DollarSign className="w-3 h-3 text-green-600" />
              <span className="text-xs font-medium">Cost</span>
            </div>
            <div className="text-lg font-bold">
              {formatCurrency(transformation.cost_usd)}
            </div>
            <div className="text-xs text-muted-foreground">Total spent</div>
          </div>

          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Timer className="w-3 h-3 text-purple-600" />
              <span className="text-xs font-medium">Duration</span>
            </div>
            <div className="text-lg font-bold">
              {transformation.execution_time_ms 
                ? formatDuration(transformation.execution_time_ms)
                : formatDistanceToNow(new Date(transformation.created_at), { addSuffix: false })
              }
            </div>
            <div className="text-xs text-muted-foreground">
              {transformation.status === 'running' ? 'Elapsed' : 'Total'}
            </div>
          </div>

          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <GitBranch className="w-3 h-3 text-orange-600" />
              <span className="text-xs font-medium">Status</span>
            </div>
            <div className={`text-lg font-bold ${getStatusColor()}`}>
              {transformation.status}
            </div>
            <div className="text-xs text-muted-foreground">
              {transformation.status === 'running' ? 'Processing...' : 'Final'}
            </div>
          </div>
        </div>

        {/* Recent Agent Logs */}
        {sortedLogs.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Recent Agent Activity</h4>
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {sortedLogs.slice(-5).reverse().map((log) => (
                  <div key={log.id} className="p-3 border rounded-lg bg-muted/50">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          Iteration {log.iteration}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          Score: {log.score.toFixed(1)}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {formatCurrency(log.cost_usd)}
                      </span>
                    </div>
                    
                    <div className="space-y-2 text-xs">
                      <div className="flex items-start space-x-2">
                        <Brain className="w-3 h-3 text-blue-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <span className="font-medium">Plan:</span>
                          <p className="text-muted-foreground mt-1 line-clamp-2">
                            {log.plan.substring(0, 100)}...
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start space-x-2">
                        <Eye className="w-3 h-3 text-purple-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <span className="font-medium">Critique:</span>
                          <p className="text-muted-foreground mt-1 line-clamp-2">
                            {log.critique.substring(0, 100)}...
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center space-x-2 pt-2 border-t">
          {onViewDetails && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDetails(transformation.id)}
              className="flex-1"
            >
              <Code className="w-3 h-3 mr-1" />
              View Details
            </Button>
          )}
          
          {transformation.github_pr_url && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(transformation.github_pr_url, '_blank')}
              className="flex-1"
            >
              <GitBranch className="w-3 h-3 mr-1" />
              View PR
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TransformationProgress;
