import { ValidationRule } from '@/hooks/useFormValidation';

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  username: /^[a-zA-Z0-9_-]{3,20}$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  hexColor: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  ipAddress: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  creditCard: /^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})$/,
  postalCode: /^[0-9]{5}(?:-[0-9]{4})?$/,
};

// Common validation rules
export const VALIDATION_RULES = {
  // Basic field validations
  required: (message = 'This field is required'): ValidationRule => ({
    required: message,
  }),

  email: (message = 'Please enter a valid email address'): ValidationRule => ({
    email: message,
  }),

  url: (message = 'Please enter a valid URL'): ValidationRule => ({
    url: message,
  }),

  // String length validations
  minLength: (length: number, message?: string): ValidationRule => ({
    minLength: {
      value: length,
      message: message || `Must be at least ${length} characters`,
    },
  }),

  maxLength: (length: number, message?: string): ValidationRule => ({
    maxLength: {
      value: length,
      message: message || `Must be no more than ${length} characters`,
    },
  }),

  length: (min: number, max: number): ValidationRule => ({
    minLength: {
      value: min,
      message: `Must be at least ${min} characters`,
    },
    maxLength: {
      value: max,
      message: `Must be no more than ${max} characters`,
    },
  }),

  // Number validations
  number: (message = 'Please enter a valid number'): ValidationRule => ({
    number: message,
  }),

  min: (value: number, message?: string): ValidationRule => ({
    min: {
      value,
      message: message || `Must be at least ${value}`,
    },
  }),

  max: (value: number, message?: string): ValidationRule => ({
    max: {
      value,
      message: message || `Must be no more than ${value}`,
    },
  }),

  range: (min: number, max: number): ValidationRule => ({
    min: {
      value: min,
      message: `Must be at least ${min}`,
    },
    max: {
      value: max,
      message: `Must be no more than ${max}`,
    },
  }),

  // Pattern validations
  pattern: (regex: RegExp, message: string): ValidationRule => ({
    pattern: {
      value: regex,
      message,
    },
  }),

  phone: (message = 'Please enter a valid phone number'): ValidationRule => ({
    pattern: {
      value: VALIDATION_PATTERNS.phone,
      message,
    },
  }),

  username: (message = 'Username must be 3-20 characters and contain only letters, numbers, hyphens, and underscores'): ValidationRule => ({
    pattern: {
      value: VALIDATION_PATTERNS.username,
      message,
    },
  }),

  slug: (message = 'Must be a valid slug (lowercase letters, numbers, and hyphens only)'): ValidationRule => ({
    pattern: {
      value: VALIDATION_PATTERNS.slug,
      message,
    },
  }),

  // Password validations
  password: (message = 'Password must be at least 8 characters and contain uppercase, lowercase, number, and special character'): ValidationRule => ({
    pattern: {
      value: VALIDATION_PATTERNS.password,
      message,
    },
  }),

  strongPassword: (): ValidationRule => ({
    minLength: {
      value: 8,
      message: 'Password must be at least 8 characters',
    },
    pattern: {
      value: VALIDATION_PATTERNS.password,
      message: 'Password must contain uppercase, lowercase, number, and special character',
    },
  }),

  // Match validation (for password confirmation)
  match: (fieldName: string, message?: string): ValidationRule => ({
    match: fieldName,
    custom: (value, allValues) => {
      if (value !== allValues[fieldName]) {
        return message || 'Values do not match';
      }
      return true;
    },
  }),

  // Custom validations
  custom: (validator: (value: any) => string | boolean, message?: string): ValidationRule => ({
    custom: (value) => {
      const result = validator(value);
      if (typeof result === 'string') return result;
      if (result === false) return message || 'Invalid value';
      return true;
    },
  }),

  // File validations
  fileSize: (maxSizeInMB: number, message?: string): ValidationRule => ({
    custom: (file: File) => {
      if (!file) return true;
      const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
      if (file.size > maxSizeInBytes) {
        return message || `File size must be less than ${maxSizeInMB}MB`;
      }
      return true;
    },
  }),

  fileType: (allowedTypes: string[], message?: string): ValidationRule => ({
    custom: (file: File) => {
      if (!file) return true;
      if (!allowedTypes.includes(file.type)) {
        return message || `File type must be one of: ${allowedTypes.join(', ')}`;
      }
      return true;
    },
  }),

  // Date validations
  futureDate: (message = 'Date must be in the future'): ValidationRule => ({
    custom: (value: string) => {
      if (!value) return true;
      const date = new Date(value);
      if (date <= new Date()) {
        return message;
      }
      return true;
    },
  }),

  pastDate: (message = 'Date must be in the past'): ValidationRule => ({
    custom: (value: string) => {
      if (!value) return true;
      const date = new Date(value);
      if (date >= new Date()) {
        return message;
      }
      return true;
    },
  }),

  minAge: (age: number, message?: string): ValidationRule => ({
    custom: (value: string) => {
      if (!value) return true;
      const birthDate = new Date(value);
      const today = new Date();
      const userAge = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        userAge--;
      }
      
      if (userAge < age) {
        return message || `Must be at least ${age} years old`;
      }
      return true;
    },
  }),
};

// Combine multiple validation rules
export const combineRules = (...rules: ValidationRule[]): ValidationRule => {
  return rules.reduce((combined, rule) => ({ ...combined, ...rule }), {});
};

// Common form schemas
export const FORM_SCHEMAS = {
  // User registration
  userRegistration: {
    firstName: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.minLength(2),
      VALIDATION_RULES.maxLength(50)
    ),
    lastName: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.minLength(2),
      VALIDATION_RULES.maxLength(50)
    ),
    email: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.email()
    ),
    username: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.username()
    ),
    password: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.strongPassword()
    ),
    confirmPassword: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.match('password', 'Passwords do not match')
    ),
  },

  // User login
  userLogin: {
    email: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.email()
    ),
    password: VALIDATION_RULES.required(),
  },

  // Contact form
  contactForm: {
    name: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.minLength(2),
      VALIDATION_RULES.maxLength(100)
    ),
    email: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.email()
    ),
    subject: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.minLength(5),
      VALIDATION_RULES.maxLength(200)
    ),
    message: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.minLength(10),
      VALIDATION_RULES.maxLength(1000)
    ),
  },

  // Profile settings
  profileSettings: {
    displayName: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.minLength(2),
      VALIDATION_RULES.maxLength(50)
    ),
    bio: VALIDATION_RULES.maxLength(500),
    website: VALIDATION_RULES.url(),
    location: VALIDATION_RULES.maxLength(100),
  },

  // Project creation
  projectCreation: {
    name: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.minLength(3),
      VALIDATION_RULES.maxLength(100)
    ),
    description: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.minLength(10),
      VALIDATION_RULES.maxLength(500)
    ),
    repository: VALIDATION_RULES.url('Please enter a valid repository URL'),
    tags: VALIDATION_RULES.custom(
      (value: string[]) => {
        if (value && value.length > 10) {
          return 'Maximum 10 tags allowed';
        }
        return true;
      }
    ),
  },

  // API key creation
  apiKeyCreation: {
    name: combineRules(
      VALIDATION_RULES.required(),
      VALIDATION_RULES.minLength(3),
      VALIDATION_RULES.maxLength(50)
    ),
    description: VALIDATION_RULES.maxLength(200),
    expiresAt: VALIDATION_RULES.futureDate('Expiration date must be in the future'),
  },
};

export default VALIDATION_RULES;
