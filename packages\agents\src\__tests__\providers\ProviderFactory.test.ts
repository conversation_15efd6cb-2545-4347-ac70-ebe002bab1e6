import { jest } from '@jest/globals';
import { ProviderFactory } from '../../providers/ProviderFactory.js';
import { createMockProviderConfig } from '../setup.js';

// Import providers to trigger registration
import '../../providers/OpenAIProvider.js';
import '../../providers/AnthropicProvider.js';
import '../../providers/VertexAIProvider.js';

describe('ProviderFactory', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('provider registration', () => {
    it('should have all providers registered', () => {
      const registeredProviders = ProviderFactory.getRegisteredProviders();
      
      expect(registeredProviders).toContain('openai');
      expect(registeredProviders).toContain('anthropic');
      expect(registeredProviders).toContain('vertex-ai');
      expect(registeredProviders.length).toBeGreaterThanOrEqual(3);
    });

    it('should create OpenAI provider successfully', () => {
      const config = createMockProviderConfig('openai');
      const provider = ProviderFactory.createProvider(config);
      
      expect(provider).toBeDefined();
      expect(provider.constructor.name).toBe('OpenAIProvider');
    });

    it('should create Anthropic provider successfully', () => {
      const config = createMockProviderConfig('anthropic');
      const provider = ProviderFactory.createProvider(config);
      
      expect(provider).toBeDefined();
      expect(provider.constructor.name).toBe('AnthropicProvider');
    });

    it('should create VertexAI provider successfully', () => {
      const config = createMockProviderConfig('vertex-ai');
      const provider = ProviderFactory.createProvider(config);
      
      expect(provider).toBeDefined();
      expect(provider.constructor.name).toBe('VertexAIProvider');
    });

    it('should throw error for unregistered provider', () => {
      const config = {
        ...createMockProviderConfig('openai'),
        type: 'unknown-provider' as any
      };
      
      expect(() => ProviderFactory.createProvider(config))
        .toThrow('Provider type \'unknown-provider\' is not registered');
    });
  });

  describe('default configurations', () => {
    it('should create default OpenAI config', () => {
      const config = ProviderFactory.getDefaultConfig('openai');

      expect(config.type).toBe('openai');
      expect(config.model).toBe('gpt-4o');
      expect(config.temperature).toBe(0.7);
      expect(config.maxTokens).toBe(2000);
      expect(config.openai?.baseURL).toBe('https://api.openai.com/v1');
    });

    it('should create default Anthropic config', () => {
      const config = ProviderFactory.getDefaultConfig('anthropic');

      expect(config.type).toBe('anthropic');
      expect(config.model).toBe('claude-opus-4-20250514');
      expect(config.temperature).toBe(0.7);
      expect(config.maxTokens).toBe(2000);
      expect(config.anthropic?.baseURL).toBe('https://api.anthropic.com');
    });

    it('should create default VertexAI config', () => {
      const config = ProviderFactory.getDefaultConfig('vertex-ai');

      expect(config.type).toBe('vertex-ai');
      expect(config.model).toBe('gemini-2.0-flash');
      expect(config.temperature).toBe(0.7);
      expect(config.maxTokens).toBe(2000);
      expect(config.vertexAI?.location).toBe('us-central1');
    });
  });

  describe('error handling', () => {
    it('should handle provider creation errors gracefully', () => {
      const invalidConfig = {
        ...createMockProviderConfig('vertex-ai'),
        vertexAI: {
          projectId: '', // Invalid - empty project ID
          location: 'us-central1'
        }
      };
      
      expect(() => ProviderFactory.createProvider(invalidConfig))
        .toThrow('Failed to create provider \'vertex-ai\'');
    });

    it('should provide helpful error messages', () => {
      try {
        ProviderFactory.createProvider({
          type: 'nonexistent' as any,
          model: 'test',
          temperature: 0.7,
          maxTokens: 1000
        });
      } catch (error) {
        expect(error.message).toContain('Provider type \'nonexistent\' is not registered');
        expect(error.message).toContain('Available providers:');
      }
    });
  });

  describe('provider validation', () => {
    it('should validate provider configs before creation', () => {
      const configs = [
        createMockProviderConfig('openai'),
        createMockProviderConfig('anthropic'),
        createMockProviderConfig('vertex-ai')
      ];

      configs.forEach(config => {
        expect(() => ProviderFactory.createProvider(config)).not.toThrow();
      });
    });

    it('should reject configs with missing required fields', () => {
      const incompleteConfig = {
        type: 'openai' as const,
        // Missing model, temperature, maxTokens
      };

      expect(() => ProviderFactory.createProvider(incompleteConfig as any))
        .toThrow();
    });
  });

  describe('provider capabilities', () => {
    it('should create providers with correct capabilities', () => {
      const openaiProvider = ProviderFactory.createProvider(createMockProviderConfig('openai'));
      const anthropicProvider = ProviderFactory.createProvider(createMockProviderConfig('anthropic'));
      const vertexProvider = ProviderFactory.createProvider(createMockProviderConfig('vertex-ai'));

      expect(openaiProvider.supportsStreaming()).toBe(true);
      expect(anthropicProvider.supportsStreaming()).toBe(true);
      expect(vertexProvider.supportsStreaming()).toBe(true);
    });

    it('should create providers with proper cost calculation', () => {
      const provider = ProviderFactory.createProvider(createMockProviderConfig('openai'));

      const mockUsage = {
        inputTokens: 100,
        outputTokens: 200,
        totalTokens: 300,
        cost: 0,
        provider: 'openai' as const,
        model: 'gpt-4o',
        timestamp: new Date()
      };

      // Test that the provider has cost calculation capability
      expect(provider).toHaveProperty('calculateCost');
      expect(typeof (provider as any).calculateCost).toBe('function');
    });
  });
});
