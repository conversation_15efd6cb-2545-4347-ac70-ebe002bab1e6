// Database types for Supabase tables

export interface Transformation {
  id: string;
  user_id: string;
  prompt: string;
  max_iterations: number;
  score_threshold: number;
  final_score?: number;
  final_patch?: any;
  iterations_count: number;
  status: 'running' | 'completed' | 'failed' | 'timeout';
  github_pr_url?: string;
  cost_usd: number;
  execution_time_ms?: number;
  created_at: string;
  completed_at?: string;
  updated_at?: string;
}

export interface AgentLog {
  id: string;
  transformation_id: string;
  user_id: string;
  iteration: number;
  plan: string;
  critique: string;
  score: number;
  patch?: any;
  tokens_used: number;
  cost_usd: number;
  created_at: string;
}

export interface UserSettings {
  id: string;
  user_id: string;
  planner_model: string;
  critic_model: string;
  default_max_iterations: number;
  default_score_threshold: number;
  telemetry_enabled: boolean;
  auto_create_pr: boolean;
  github_repo_owner?: string;
  github_repo_name?: string;
  openai_api_key_encrypted?: string;
  anthropic_api_key_encrypted?: string;
  provider_configs?: {
    planner: {
      type: string;
      model: string;
      temperature: number;
      maxTokens: number;
    };
    critic: {
      type: string;
      model: string;
      temperature: number;
      maxTokens: number;
    };
    fallback_providers: string[];
  };
  cost_guard_config?: {
    enabled: boolean;
    maxCostPerLoop: number;
    alertThresholds: {
      nearLimitWarning: number;
      costPerHour: number;
    };
  };
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  email?: string;
  full_name?: string;
  avatar_url?: string;
  github_username?: string;
  created_at: string;
  updated_at: string;
}

export interface GitHubToken {
  id: string;
  user_id: string;
  access_token_encrypted: string;
  refresh_token_encrypted?: string;
  token_type: string;
  scope: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

export interface SystemHealth {
  id: string;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  response_time: number;
  active_connections: number;
  error_rate: number;
  timestamp: string;
}

export interface ProjectMetrics {
  id: string;
  user_id: string;
  project_name: string;
  repository_url?: string;
  lines_of_code: number;
  files_count: number;
  complexity_score: number;
  test_coverage: number;
  build_status: 'success' | 'failed' | 'pending';
  deployment_status: 'deployed' | 'failed' | 'pending';
  performance_score: number;
  security_score: number;
  timestamp: string;
}

export interface NotificationData {
  id: string;
  user_id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'system';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  read: boolean;
  dismissible: boolean;
  action_url?: string;
  action_label?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface TelemetryEvent {
  id: string;
  user_id: string;
  event_type: string;
  event_data: Record<string, any>;
  session_id?: string;
  user_agent?: string;
  ip_address?: string;
  created_at: string;
}

export interface AutonomousRepository {
  id: string;
  owner: string;
  name: string;
  branch: string;
  enabled: boolean;
  scan_interval: number;
  quality_threshold: number;
  cost_limit: number;
  last_scan?: string;
  last_commit?: string;
  improvement_targets: string[];
  exclude_patterns: string[];
  created_at: string;
  updated_at: string;
  user_id: string;
}

export interface ImprovementOpportunity {
  id: string;
  repository_id: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  file_path: string;
  line_number?: number;
  estimated_effort: 'low' | 'medium' | 'high';
  estimated_impact: 'low' | 'medium' | 'high';
  confidence_score: number;
  auto_fixable: boolean;
  fix_suggestion?: string;
  status: 'open' | 'in_progress' | 'fixed' | 'dismissed';
  created_at: string;
  updated_at: string;
}

export interface AutonomousJob {
  id: string;
  repository_id: string;
  opportunity_id?: string;
  job_type: 'scan' | 'improve' | 'test' | 'pr';
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  priority: number;
  estimated_cost: number;
  actual_cost: number;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  result_data?: Record<string, any>;
  retry_count: number;
  max_retries: number;
  created_at: string;
}

export interface CostLimit {
  repository_id: string;
  daily_limit: number;
  weekly_limit: number;
  monthly_limit: number;
  per_operation_limit: number;
  alert_threshold: number;
  enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface CostAlert {
  id: string;
  repository_id: string;
  alert_type: 'approaching_limit' | 'limit_exceeded' | 'budget_exhausted' | 'unusual_spending';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  current_cost: number;
  limit_value: number;
  percentage: number;
  acknowledged: boolean;
  acknowledged_at?: string;
  acknowledged_by?: string;
  created_at: string;
}

export interface CostBudget {
  id: string;
  repository_id: string;
  budget_type: 'daily' | 'weekly' | 'monthly';
  budget_date: string;
  allocated_amount: number;
  spent_amount: number;
  remaining_amount: number;
  utilization_percentage: number;
  status: 'active' | 'exceeded' | 'exhausted';
  created_at: string;
  updated_at: string;
}

// Real-time subscription types
export interface RealtimePayload<T = any> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  errors: any[];
}

export interface SubscriptionOptions {
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  schema?: string;
  table?: string;
  filter?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  error?: {
    message: string;
    code?: string;
    details?: any;
  };
  count?: number;
  status?: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Dashboard data aggregation types
export interface DashboardStats {
  totalTransformations: number;
  completedTransformations: number;
  runningTransformations: number;
  failedTransformations: number;
  totalCost: number;
  averageScore: number;
  averageExecutionTime: number;
  recentActivity: Transformation[];
}

export interface SystemMetrics {
  health: SystemHealth;
  performance: {
    avgResponseTime: number;
    errorRate: number;
    throughput: number;
  };
  resources: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
  };
  connections: {
    active: number;
    total: number;
  };
}

export interface UserAnalytics {
  transformationsOverTime: Array<{
    date: string;
    count: number;
    cost: number;
  }>;
  modelUsage: Array<{
    model: string;
    count: number;
    cost: number;
  }>;
  successRate: number;
  averageIterations: number;
  topRepositories: Array<{
    repository: string;
    transformations: number;
  }>;
}
