import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  MemoryStick, 
  Wifi, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { useDashboardRealtime } from '@/hooks/useRealtimeData';
import { dataService } from '@/lib/supabase/dataService';
import type { SystemHealth } from '@/lib/supabase/types';

interface SystemHealthMonitorProps {
  compact?: boolean;
  showHistory?: boolean;
  refreshInterval?: number;
}

interface HealthMetric {
  label: string;
  value: number;
  unit: string;
  icon: React.ComponentType<any>;
  threshold: {
    warning: number;
    critical: number;
  };
  trend?: 'up' | 'down' | 'stable';
}

export const SystemHealthMonitor: React.FC<SystemHealthMonitorProps> = ({
  compact = false,
  showHistory = true,
  refreshInterval = 30000 // 30 seconds
}) => {
  const { systemHealth, connectionStatus, refresh } = useDashboardRealtime();
  const [healthHistory, setHealthHistory] = useState<SystemHealth[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Load health history
  useEffect(() => {
    const loadHistory = async () => {
      try {
        const history = await dataService.getSystemHealthHistory(24); // Last 24 hours
        setHealthHistory(history);
      } catch (error) {
        console.error('Failed to load system health history:', error);
      }
    };

    if (showHistory) {
      loadHistory();
    }
  }, [showHistory]);

  // Auto-refresh
  useEffect(() => {
    const interval = setInterval(() => {
      handleRefresh();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  // Update last update time when system health changes
  useEffect(() => {
    if (systemHealth) {
      setLastUpdate(new Date(systemHealth.timestamp));
    }
  }, [systemHealth]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refresh();
      if (showHistory) {
        const history = await dataService.getSystemHealthHistory(24);
        setHealthHistory(history);
      }
    } catch (error) {
      console.error('Failed to refresh system health:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getHealthStatus = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'critical';
    if (value >= thresholds.warning) return 'warning';
    return 'healthy';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      case 'healthy': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'critical': return 'destructive' as const;
      case 'warning': return 'secondary' as const;
      case 'healthy': return 'default' as const;
      default: return 'outline' as const;
    }
  };

  const getTrend = (current: number, history: SystemHealth[], key: keyof SystemHealth) => {
    if (history.length < 2) return 'stable';
    
    const previous = history[1];
    const currentValue = current;
    const previousValue = previous[key] as number;
    
    if (currentValue > previousValue * 1.05) return 'up';
    if (currentValue < previousValue * 0.95) return 'down';
    return 'stable';
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-3 h-3" />;
      case 'down': return <TrendingDown className="w-3 h-3" />;
      case 'stable': return <Minus className="w-3 h-3" />;
    }
  };

  if (!systemHealth) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5" />
              <CardTitle>System Health</CardTitle>
            </div>
            <Badge variant="outline">No Data</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              System health data is not available. The monitoring service may be offline.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const metrics: HealthMetric[] = [
    {
      label: 'CPU Usage',
      value: systemHealth.cpu_usage,
      unit: '%',
      icon: Cpu,
      threshold: { warning: 70, critical: 90 },
      trend: getTrend(systemHealth.cpu_usage, healthHistory, 'cpu_usage')
    },
    {
      label: 'Memory Usage',
      value: systemHealth.memory_usage,
      unit: '%',
      icon: MemoryStick,
      threshold: { warning: 80, critical: 95 },
      trend: getTrend(systemHealth.memory_usage, healthHistory, 'memory_usage')
    },
    {
      label: 'Disk Usage',
      value: systemHealth.disk_usage,
      unit: '%',
      icon: HardDrive,
      threshold: { warning: 85, critical: 95 },
      trend: getTrend(systemHealth.disk_usage, healthHistory, 'disk_usage')
    },
    {
      label: 'Response Time',
      value: systemHealth.response_time,
      unit: 'ms',
      icon: Wifi,
      threshold: { warning: 500, critical: 1000 },
      trend: getTrend(systemHealth.response_time, healthHistory, 'response_time')
    }
  ];

  const overallStatus = metrics.reduce((worst, metric) => {
    const status = getHealthStatus(metric.value, metric.threshold);
    if (status === 'critical') return 'critical';
    if (status === 'warning' && worst !== 'critical') return 'warning';
    return worst;
  }, 'healthy');

  if (compact) {
    return (
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Activity className="w-4 h-4" />
          <Badge variant={getStatusBadgeVariant(overallStatus)}>
            {overallStatus === 'healthy' ? (
              <>
                <CheckCircle className="w-3 h-3 mr-1" />
                Healthy
              </>
            ) : (
              <>
                <AlertTriangle className="w-3 h-3 mr-1" />
                {overallStatus}
              </>
            )}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-3 text-sm">
          {metrics.slice(0, 2).map((metric) => (
            <div key={metric.label} className="flex items-center space-x-1">
              <metric.icon className="w-3 h-3" />
              <span className={getStatusColor(getHealthStatus(metric.value, metric.threshold))}>
                {metric.value}{metric.unit}
              </span>
            </div>
          ))}
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
        </Button>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <CardTitle>System Health</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={getStatusBadgeVariant(overallStatus)}>
              {overallStatus === 'healthy' ? (
                <>
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Healthy
                </>
              ) : (
                <>
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  {overallStatus.charAt(0).toUpperCase() + overallStatus.slice(1)}
                </>
              )}
            </Badge>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        <CardDescription>
          Real-time system performance metrics
          {lastUpdate && (
            <span className="block text-xs text-muted-foreground mt-1">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Connection Status */}
        <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' : 
              connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : 
              'bg-red-500'
            }`} />
            <span className="text-sm font-medium">
              {connectionStatus === 'connected' ? 'Connected' : 
               connectionStatus === 'connecting' ? 'Connecting...' : 
               'Disconnected'}
            </span>
          </div>
          <div className="text-xs text-muted-foreground">
            {systemHealth.active_connections} active connections
          </div>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {metrics.map((metric) => {
            const status = getHealthStatus(metric.value, metric.threshold);
            return (
              <div key={metric.label} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <metric.icon className="w-4 h-4" />
                    <span className="text-sm font-medium">{metric.label}</span>
                    {getTrendIcon(metric.trend)}
                  </div>
                  <span className={`text-sm font-mono ${getStatusColor(status)}`}>
                    {metric.value}{metric.unit}
                  </span>
                </div>
                <Progress 
                  value={metric.value} 
                  className={`h-2 ${
                    status === 'critical' ? '[&>div]:bg-red-500' :
                    status === 'warning' ? '[&>div]:bg-yellow-500' :
                    '[&>div]:bg-green-500'
                  }`}
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Warning: {metric.threshold.warning}{metric.unit}</span>
                  <span>Critical: {metric.threshold.critical}{metric.unit}</span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Additional Metrics */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t">
          <div className="text-center">
            <div className="text-2xl font-bold text-foreground">
              {systemHealth.error_rate.toFixed(2)}%
            </div>
            <div className="text-sm text-muted-foreground">Error Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-foreground">
              {systemHealth.active_connections}
            </div>
            <div className="text-sm text-muted-foreground">Active Connections</div>
          </div>
        </div>

        {/* Alerts */}
        {overallStatus !== 'healthy' && (
          <Alert variant={overallStatus === 'critical' ? 'destructive' : 'default'}>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {overallStatus === 'critical' 
                ? 'Critical system issues detected. Immediate attention required.'
                : 'System performance warnings detected. Monitor closely.'
              }
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default SystemHealthMonitor;
