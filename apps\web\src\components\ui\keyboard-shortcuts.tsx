import * as React from "react"
import { cn } from "@/lib/utils"
import { HelpIcon, CloseIcon } from "@/components/ui/icon"
import { Button } from "@/components/ui/button"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"

interface KeyboardShortcut {
  key: string
  description: string
  category?: string
  action?: () => void
}

interface KeyboardShortcutsProps {
  shortcuts: KeyboardShortcut[]
  className?: string
}

// Keyboard shortcut display component
export function KeyboardShortcuts({ shortcuts, className }: KeyboardShortcutsProps) {
  const groupedShortcuts = React.useMemo(() => {
    const groups: Record<string, KeyboardShortcut[]> = {}
    shortcuts.forEach(shortcut => {
      const category = shortcut.category || 'General'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(shortcut)
    })
    return groups
  }, [shortcuts])

  return (
    <div className={cn("space-y-6", className)}>
      {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
        <VStack key={category} spacing="sm">
          <Typography variant="h6" className="text-foreground font-semibold">
            {category}
          </Typography>
          <VStack spacing="xs">
            {categoryShortcuts.map((shortcut, index) => (
              <HStack key={index} spacing="md" className="justify-between">
                <Typography variant="body-sm" className="text-muted-foreground">
                  {shortcut.description}
                </Typography>
                <KeyboardKey keys={shortcut.key} />
              </HStack>
            ))}
          </VStack>
        </VStack>
      ))}
    </div>
  )
}

// Individual keyboard key display
interface KeyboardKeyProps {
  keys: string
  className?: string
}

export function KeyboardKey({ keys, className }: KeyboardKeyProps) {
  const keyParts = keys.split('+').map(key => key.trim())
  
  return (
    <HStack spacing="xs" className={cn("items-center", className)}>
      {keyParts.map((key, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <span className="text-muted-foreground text-xs">+</span>
          )}
          <kbd className="inline-flex items-center justify-center px-2 py-1 text-xs font-mono bg-muted border border-border rounded">
            {formatKey(key)}
          </kbd>
        </React.Fragment>
      ))}
    </HStack>
  )
}

function formatKey(key: string): string {
  const keyMap: Record<string, string> = {
    'cmd': '⌘',
    'ctrl': 'Ctrl',
    'alt': 'Alt',
    'shift': 'Shift',
    'enter': '↵',
    'escape': 'Esc',
    'space': 'Space',
    'tab': 'Tab',
    'backspace': '⌫',
    'delete': 'Del',
    'up': '↑',
    'down': '↓',
    'left': '←',
    'right': '→'
  }
  
  return keyMap[key.toLowerCase()] || key.toUpperCase()
}

// Keyboard shortcuts modal/panel
interface KeyboardShortcutsModalProps {
  isOpen: boolean
  onClose: () => void
  shortcuts: KeyboardShortcut[]
}

export function KeyboardShortcutsModal({ isOpen, onClose, shortcuts }: KeyboardShortcutsModalProps) {
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative w-full max-w-2xl max-h-[80vh] mx-4 bg-card border border-border rounded-lg shadow-lg overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <HStack spacing="sm" align="center">
            <HelpIcon />
            <Typography variant="h4">Keyboard Shortcuts</Typography>
          </HStack>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="focus-ring"
          >
            <CloseIcon />
          </Button>
        </div>
        
        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <KeyboardShortcuts shortcuts={shortcuts} />
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t border-border bg-muted/50">
          <Typography variant="body-sm" className="text-muted-foreground text-center">
            Press <KeyboardKey keys="Escape" className="inline-flex" /> to close
          </Typography>
        </div>
      </div>
    </div>
  )
}

// Default shortcuts for the Metamorphic Reactor
export const defaultShortcuts: KeyboardShortcut[] = [
  // Reactor Controls
  { key: 'Ctrl+Enter', description: 'Run reactor loop', category: 'Reactor' },
  { key: 'Ctrl+Shift+S', description: 'Stop reactor loop', category: 'Reactor' },
  { key: 'Ctrl+Shift+A', description: 'Apply changes', category: 'Reactor' },
  { key: 'Ctrl+Shift+C', description: 'Clear logs', category: 'Reactor' },
  
  // Navigation
  { key: 'Ctrl+1', description: 'Focus prompt editor', category: 'Navigation' },
  { key: 'Ctrl+2', description: 'Focus diff viewer', category: 'Navigation' },
  { key: 'Ctrl+3', description: 'Focus console', category: 'Navigation' },
  { key: 'Ctrl+B', description: 'Toggle examples panel', category: 'Navigation' },
  { key: 'Ctrl+H', description: 'Toggle help panel', category: 'Navigation' },
  { key: 'Ctrl+M', description: 'Open monitoring dashboard', category: 'Navigation' },
  
  // File Operations
  { key: 'Ctrl+S', description: 'Download code', category: 'File' },
  { key: 'Ctrl+O', description: 'Load example', category: 'File' },
  { key: 'Ctrl+Shift+P', description: 'Create pull request', category: 'File' },
  
  // General
  { key: 'Ctrl+/', description: 'Show keyboard shortcuts', category: 'General' },
  { key: 'Escape', description: 'Close modal/panel', category: 'General' },
  { key: 'Ctrl+K', description: 'Command palette', category: 'General' },
]

// Hook for managing keyboard shortcuts
export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[]) {
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const key = [
        e.ctrlKey && 'Ctrl',
        e.shiftKey && 'Shift', 
        e.altKey && 'Alt',
        e.metaKey && 'Cmd',
        e.key
      ].filter(Boolean).join('+')

      const shortcut = shortcuts.find(s => s.key === key)
      if (shortcut?.action) {
        e.preventDefault()
        shortcut.action()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [shortcuts])
}

// Keyboard shortcuts provider component
interface KeyboardShortcutsProviderProps {
  children: React.ReactNode
  shortcuts?: KeyboardShortcut[]
}

export function KeyboardShortcutsProvider({ 
  children, 
  shortcuts = defaultShortcuts 
}: KeyboardShortcutsProviderProps) {
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  
  const enhancedShortcuts = React.useMemo(() => [
    ...shortcuts,
    {
      key: 'Ctrl+/',
      description: 'Show keyboard shortcuts',
      category: 'General',
      action: () => setIsModalOpen(true)
    }
  ], [shortcuts])

  useKeyboardShortcuts(enhancedShortcuts)

  return (
    <>
      {children}
      <KeyboardShortcutsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        shortcuts={enhancedShortcuts}
      />
    </>
  )
}
