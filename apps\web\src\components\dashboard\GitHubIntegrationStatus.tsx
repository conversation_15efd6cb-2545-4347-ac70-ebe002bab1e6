import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Github, 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw,
  ExternalLink,
  Settings,
  GitBranch,
  Star,
  Users
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useGitHubAuth } from '@/hooks/useGitHubAuth';
import { githubService } from '@/lib/github';

// Create a singleton instance
const githubServiceInstance = new githubService();

interface GitHubUser {
  login: string;
  name: string;
  avatar_url: string;
  public_repos: number;
  followers: number;
  following: number;
  bio?: string;
  company?: string;
  location?: string;
}

interface GitHubIntegrationStatusProps {
  onConnect?: () => void;
  onDisconnect?: () => void;
  showActions?: boolean;
  compact?: boolean;
}

export const GitHubIntegrationStatus: React.FC<GitHubIntegrationStatusProps> = ({
  onConnect,
  onDisconnect,
  showActions = true,
  compact = false
}) => {
  const { toast } = useToast();
  const { isConnected, user, isLoading, connectGitHub, disconnectGitHub } = useGitHubAuth();
  const [userDetails, setUserDetails] = useState<GitHubUser | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [rateLimitInfo, setRateLimitInfo] = useState<{
    remaining: number;
    limit: number;
    resetTime: Date;
  } | null>(null);

  useEffect(() => {
    if (isConnected && user) {
      fetchUserDetails();
      fetchRateLimitInfo();
    }
  }, [isConnected, user]);

  const fetchUserDetails = async () => {
    try {
      const details = await githubServiceInstance.makeAuthenticatedRequest('/user');
      setUserDetails(details);
    } catch (error) {
      console.error('Failed to fetch GitHub user details:', error);
    }
  };

  const fetchRateLimitInfo = async () => {
    try {
      const rateLimit = await githubServiceInstance.makeAuthenticatedRequest('/rate_limit');
      setRateLimitInfo({
        remaining: rateLimit.rate.remaining,
        limit: rateLimit.rate.limit,
        resetTime: new Date(rateLimit.rate.reset * 1000)
      });
    } catch (error) {
      console.error('Failed to fetch rate limit info:', error);
    }
  };

  const handleConnect = async () => {
    try {
      await connectGitHub();
      onConnect?.();
      toast({
        title: "GitHub Connected",
        description: "Successfully connected to GitHub",
      });
    } catch (error) {
      toast({
        title: "Connection Failed",
        description: error instanceof Error ? error.message : "Failed to connect to GitHub",
        variant: "destructive",
      });
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnectGitHub();
      setUserDetails(null);
      setRateLimitInfo(null);
      onDisconnect?.();
      toast({
        title: "GitHub Disconnected",
        description: "Successfully disconnected from GitHub",
      });
    } catch (error) {
      toast({
        title: "Disconnection Failed",
        description: error instanceof Error ? error.message : "Failed to disconnect from GitHub",
        variant: "destructive",
      });
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([
        fetchUserDetails(),
        fetchRateLimitInfo()
      ]);
      toast({
        title: "Refreshed",
        description: "GitHub integration status updated",
      });
    } catch (error) {
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh GitHub status",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const getRateLimitColor = () => {
    if (!rateLimitInfo) return 'text-muted-foreground';
    const percentage = (rateLimitInfo.remaining / rateLimitInfo.limit) * 100;
    if (percentage > 50) return 'text-green-600';
    if (percentage > 20) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (compact) {
    return (
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <Github className="w-4 h-4" />
          <Badge variant={isConnected ? "default" : "secondary"}>
            {isConnected ? (
              <>
                <CheckCircle className="w-3 h-3 mr-1" />
                Connected
              </>
            ) : (
              <>
                <AlertTriangle className="w-3 h-3 mr-1" />
                Not Connected
              </>
            )}
          </Badge>
        </div>
        
        {isConnected && userDetails && (
          <div className="flex items-center space-x-2">
            <Avatar className="w-6 h-6">
              <AvatarImage src={userDetails.avatar_url} alt={userDetails.login} />
              <AvatarFallback>{userDetails.login[0].toUpperCase()}</AvatarFallback>
            </Avatar>
            <span className="text-sm font-medium">{userDetails.login}</span>
          </div>
        )}
        
        {showActions && (
          <Button
            size="sm"
            variant="outline"
            onClick={isConnected ? handleDisconnect : handleConnect}
            disabled={isLoading}
          >
            {isConnected ? 'Disconnect' : 'Connect'}
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Github className="w-5 h-5" />
            <CardTitle className="text-lg">GitHub Integration</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={isConnected ? "default" : "secondary"}>
              {isConnected ? (
                <>
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Connected
                </>
              ) : (
                <>
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Not Connected
                </>
              )}
            </Badge>
            {isConnected && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            )}
          </div>
        </div>
        <CardDescription>
          {isConnected 
            ? "GitHub account connected and ready for repository operations"
            : "Connect your GitHub account to enable repository access and code transformations"
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isConnected ? (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              GitHub integration is required for repository access, code analysis, and automated transformations.
            </AlertDescription>
          </Alert>
        ) : userDetails ? (
          <div className="space-y-4">
            {/* User Info */}
            <div className="flex items-start space-x-4">
              <Avatar className="w-12 h-12">
                <AvatarImage src={userDetails.avatar_url} alt={userDetails.login} />
                <AvatarFallback>{userDetails.login[0].toUpperCase()}</AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-1">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold">{userDetails.name || userDetails.login}</h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => window.open(`https://github.com/${userDetails.login}`, '_blank')}
                  >
                    <ExternalLink className="w-3 h-3" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">@{userDetails.login}</p>
                {userDetails.bio && (
                  <p className="text-sm">{userDetails.bio}</p>
                )}
                {(userDetails.company || userDetails.location) && (
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    {userDetails.company && <span>{userDetails.company}</span>}
                    {userDetails.location && <span>{userDetails.location}</span>}
                  </div>
                )}
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <div className="flex items-center justify-center space-x-1">
                  <GitBranch className="w-3 h-3" />
                  <span className="text-sm font-medium">{userDetails.public_repos}</span>
                </div>
                <p className="text-xs text-muted-foreground">Repositories</p>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-center space-x-1">
                  <Users className="w-3 h-3" />
                  <span className="text-sm font-medium">{userDetails.followers}</span>
                </div>
                <p className="text-xs text-muted-foreground">Followers</p>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-center space-x-1">
                  <Star className="w-3 h-3" />
                  <span className="text-sm font-medium">{userDetails.following}</span>
                </div>
                <p className="text-xs text-muted-foreground">Following</p>
              </div>
            </div>

            {/* Rate Limit Info */}
            {rateLimitInfo && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span>API Rate Limit</span>
                  <span className={getRateLimitColor()}>
                    {rateLimitInfo.remaining} / {rateLimitInfo.limit}
                  </span>
                </div>
                <div className="mt-1 text-xs text-muted-foreground">
                  Resets at {rateLimitInfo.resetTime.toLocaleTimeString()}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-4">
            <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading GitHub user information...</p>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex space-x-2 pt-2">
            {isConnected ? (
              <>
                <Button
                  variant="outline"
                  onClick={handleDisconnect}
                  disabled={isLoading}
                  className="flex-1"
                >
                  Disconnect GitHub
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.open('https://github.com/settings/applications', '_blank')}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Manage
                </Button>
              </>
            ) : (
              <Button
                onClick={handleConnect}
                disabled={isLoading}
                className="w-full"
              >
                <Github className="w-4 h-4 mr-2" />
                Connect GitHub Account
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GitHubIntegrationStatus;
