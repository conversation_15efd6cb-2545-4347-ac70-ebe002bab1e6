
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">24.37% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>117/480</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.75% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>33/176</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">21.13% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>26/123</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.05% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>110/439</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="CostGuard.ts"><a href="CostGuard.ts.html">CostGuard.ts</a></td>
	<td data-value="98.07" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 98%"></div><div class="cover-empty" style="width: 2%"></div></div>
	</td>
	<td data-value="98.07" class="pct high">98.07%</td>
	<td data-value="104" class="abs high">102/104</td>
	<td data-value="80" class="pct medium">80%</td>
	<td data-value="40" class="abs medium">32/40</td>
	<td data-value="96.15" class="pct high">96.15%</td>
	<td data-value="26" class="abs high">25/26</td>
	<td data-value="98.95" class="pct high">98.95%</td>
	<td data-value="96" class="abs high">95/96</td>
	</tr>

<tr>
	<td class="file low" data-value="ProviderFailover.ts"><a href="ProviderFailover.ts.html">ProviderFailover.ts</a></td>
	<td data-value="4.03" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 4%"></div><div class="cover-empty" style="width: 96%"></div></div>
	</td>
	<td data-value="4.03" class="pct low">4.03%</td>
	<td data-value="124" class="abs low">5/124</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="48" class="abs low">0/48</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="28" class="abs low">0/28</td>
	<td data-value="4.2" class="pct low">4.2%</td>
	<td data-value="119" class="abs low">5/119</td>
	</tr>

<tr>
	<td class="file low" data-value="RetryUtil.ts"><a href="RetryUtil.ts.html">RetryUtil.ts</a></td>
	<td data-value="3.48" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.48" class="pct low">3.48%</td>
	<td data-value="86" class="abs low">3/86</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="27" class="abs low">0/27</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="23" class="abs low">0/23</td>
	<td data-value="3.75" class="pct low">3.75%</td>
	<td data-value="80" class="abs low">3/80</td>
	</tr>

<tr>
	<td class="file low" data-value="TokenMonitor.ts"><a href="TokenMonitor.ts.html">TokenMonitor.ts</a></td>
	<td data-value="4.21" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 4%"></div><div class="cover-empty" style="width: 96%"></div></div>
	</td>
	<td data-value="4.21" class="pct low">4.21%</td>
	<td data-value="166" class="abs low">7/166</td>
	<td data-value="1.63" class="pct low">1.63%</td>
	<td data-value="61" class="abs low">1/61</td>
	<td data-value="2.17" class="pct low">2.17%</td>
	<td data-value="46" class="abs low">1/46</td>
	<td data-value="4.86" class="pct low">4.86%</td>
	<td data-value="144" class="abs low">7/144</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-29T04:58:16.161Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    