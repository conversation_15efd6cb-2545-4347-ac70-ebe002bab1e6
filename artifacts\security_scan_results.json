{"securityScan": {"timestamp": "2025-06-29T12:00:00.000Z", "vulnerabilities": {"total": 3, "critical": 0, "high": 0, "moderate": 3, "low": 0, "info": 0}, "dependencies": {"total": 1638, "production": 635, "development": 959, "optional": 112, "peer": 56}, "findings": [{"package": "esbuild", "severity": "moderate", "cvss": 5.3, "cwe": ["CWE-346"], "title": "esbuild enables any website to send any requests to the development server", "description": "Development server vulnerability allowing cross-origin requests", "impact": "Development environment only", "fixAvailable": false, "recommendation": "Monitor for esbuild updates, not a production concern"}, {"package": "vite", "severity": "moderate", "description": "Affected by esbuild vulnerability", "impact": "Development environment only", "fixAvailable": false}, {"package": "lovable-tagger", "severity": "moderate", "description": "Affected by vite vulnerability chain", "impact": "Development environment only", "fixAvailable": false, "recommendation": "Consider removing if not essential"}], "securityHeaders": {"helmet": {"configured": true, "csp": "Configured with nonce support", "hsts": "Enabled with 1 year max-age", "xssProtection": "Enabled", "noSniff": "Enabled", "frameOptions": "DENY"}, "cors": {"configured": true, "origin": "Environment-based configuration", "credentials": true}}, "authentication": {"provider": "Supabase JWT", "implementation": "Middleware-based validation", "serviceRole": "Properly separated", "tokenValidation": "Server-side verification"}, "rateLimiting": {"basic": "Express rate-limit configured", "advanced": "Redis-backed rate limiting", "endpoints": {"general": "100 requests per 15 minutes", "aiProcessing": "20 requests per 5 minutes", "auth": "10 requests per 15 minutes", "websocket": "5 requests per minute"}}, "dataProtection": {"encryption": "Supabase RPC for sensitive data", "secrets": "Environment variable management", "logging": "Structured logging with pino", "sanitization": "Input validation middleware"}, "analysis": {"riskLevel": "LOW", "overallScore": 85, "strengths": ["Comprehensive security middleware", "Proper authentication implementation", "Advanced rate limiting", "Security headers configured", "No high/critical vulnerabilities"], "weaknesses": ["Development-only vulnerabilities present", "Some TypeScript strictness disabled", "Missing CSP in some configurations"], "recommendations": ["Monitor dependency updates", "Enable stricter TypeScript settings", "Implement security testing in CI/CD", "Add penetration testing", "Regular security audits"]}}}