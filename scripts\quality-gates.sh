#!/bin/bash

# Quality Gates Validation Script
# This script runs all quality checks locally before pushing to CI

set -e

echo "🎯 Running Quality Gates Validation"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Track results
RESULTS=()

# Function to log results
log_result() {
    local test_name="$1"
    local status="$2"
    local message="$3"
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ $test_name: $message${NC}"
        RESULTS+=("✅ $test_name")
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $test_name: $message${NC}"
        RESULTS+=("⚠️  $test_name")
    else
        echo -e "${RED}❌ $test_name: $message${NC}"
        RESULTS+=("❌ $test_name")
    fi
}

# 1. Test Coverage Check
echo -e "${BLUE}📊 Checking Test Coverage...${NC}"
cd apps/web
if npm run test:coverage > /dev/null 2>&1; then
    if [ -f "coverage/coverage-summary.json" ]; then
        COVERAGE=$(node -e "
            const fs = require('fs');
            const coverage = JSON.parse(fs.readFileSync('coverage/coverage-summary.json', 'utf8'));
            console.log(coverage.total.lines.pct);
        ")
        if (( $(echo "$COVERAGE >= 90" | bc -l) )); then
            log_result "Test Coverage" "PASS" "${COVERAGE}% (≥90%)"
        else
            log_result "Test Coverage" "FAIL" "${COVERAGE}% (below 90%)"
        fi
    else
        log_result "Test Coverage" "FAIL" "Coverage report not found"
    fi
else
    log_result "Test Coverage" "FAIL" "Tests failed to run"
fi
cd ..

# 2. Bundle Size Check
echo -e "${BLUE}📦 Checking Bundle Size...${NC}"
cd apps/web
if npm run build > /dev/null 2>&1; then
    BUNDLE_SIZE=$(find dist/assets -name "*.js" -exec gzip -c {} \; | wc -c)
    BUNDLE_SIZE_KB=$((BUNDLE_SIZE / 1024))
    
    if [ "$BUNDLE_SIZE_KB" -le 900 ]; then
        log_result "Bundle Size" "PASS" "${BUNDLE_SIZE_KB}KB (≤900KB)"
    else
        log_result "Bundle Size" "FAIL" "${BUNDLE_SIZE_KB}KB (exceeds 900KB)"
    fi
else
    log_result "Bundle Size" "FAIL" "Build failed"
fi
cd ..

# 3. Lint Check
echo -e "${BLUE}🔍 Running Linter...${NC}"
cd apps/web
if npm run lint > /dev/null 2>&1; then
    log_result "ESLint" "PASS" "No linting errors"
else
    log_result "ESLint" "FAIL" "Linting errors found"
fi
cd ..

# 4. Type Check
echo -e "${BLUE}🔧 Checking TypeScript...${NC}"
cd apps/web
if npx tsc --noEmit > /dev/null 2>&1; then
    log_result "TypeScript" "PASS" "No type errors"
else
    log_result "TypeScript" "FAIL" "Type errors found"
fi
cd ..

# 5. Security Audit
echo -e "${BLUE}🔒 Running Security Audit...${NC}"
if npm audit --audit-level moderate > /dev/null 2>&1; then
    log_result "Security Audit" "PASS" "No moderate+ vulnerabilities"
else
    log_result "Security Audit" "WARN" "Vulnerabilities found (check npm audit)"
fi

# 6. Accessibility Check (if server is running)
echo -e "${BLUE}♿ Checking Accessibility...${NC}"
if curl -s http://localhost:8080 > /dev/null 2>&1; then
    if command -v axe-core-cli > /dev/null 2>&1; then
        if axe-core-cli http://localhost:8080 --threshold 97 > /dev/null 2>&1; then
            log_result "Accessibility" "PASS" "Score ≥97%"
        else
            log_result "Accessibility" "FAIL" "Score <97%"
        fi
    else
        log_result "Accessibility" "WARN" "axe-core-cli not installed"
    fi
else
    log_result "Accessibility" "WARN" "Dev server not running (start with npm run dev)"
fi

# Summary
echo ""
echo "🎯 Quality Gates Summary"
echo "========================"
for result in "${RESULTS[@]}"; do
    echo "$result"
done

# Check if any tests failed
FAILED_COUNT=$(printf '%s\n' "${RESULTS[@]}" | grep -c "❌" || true)
if [ "$FAILED_COUNT" -gt 0 ]; then
    echo ""
    echo -e "${RED}❌ $FAILED_COUNT quality gate(s) failed${NC}"
    echo "Please fix the issues before pushing to CI"
    exit 1
else
    echo ""
    echo -e "${GREEN}✅ All quality gates passed! Ready for CI/CD 🚀${NC}"
fi
