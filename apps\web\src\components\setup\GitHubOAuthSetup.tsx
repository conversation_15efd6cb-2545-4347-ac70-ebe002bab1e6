import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Github, 
  ExternalLink, 
  Copy, 
  CheckCircle, 
  AlertTriangle,
  Settings,
  Key,
  Globe
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface GitHubOAuthSetupProps {
  onComplete?: () => void;
}

export const GitHubOAuthSetup: React.FC<GitHubOAuthSetupProps> = ({ onComplete }) => {
  const { toast } = useToast();
  const [clientId, setClientId] = useState('');
  const [isConfigured, setIsConfigured] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationStatus, setValidationStatus] = useState<'idle' | 'valid' | 'invalid'>('idle');

  const redirectUri = `${window.location.origin}/auth/github/callback`;
  const currentDomain = window.location.origin;

  useEffect(() => {
    // Check if GitHub OAuth is already configured
    const existingClientId = import.meta.env.VITE_GITHUB_CLIENT_ID;
    if (existingClientId && existingClientId !== 'your-github-client-id') {
      setClientId(existingClientId);
      setIsConfigured(true);
      setValidationStatus('valid');
    }
  }, []);

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      });
    } catch (err) {
      toast({
        title: "Copy failed",
        description: "Please copy the text manually",
        variant: "destructive",
      });
    }
  };

  const validateClientId = async () => {
    if (!clientId.trim()) {
      setValidationStatus('invalid');
      return;
    }

    setIsValidating(true);
    try {
      // Test the client ID by making a request to GitHub's OAuth endpoint
      const testUrl = `https://github.com/login/oauth/authorize?client_id=${clientId}&scope=user:email&state=test`;
      
      // We can't actually test this without triggering OAuth, so we'll do basic validation
      if (clientId.match(/^[a-f0-9]{20}$/i) || clientId.startsWith('Iv1.') || clientId.startsWith('Ov23li')) {
        setValidationStatus('valid');
        setIsConfigured(true);
        toast({
          title: "Client ID Valid",
          description: "GitHub Client ID format appears correct",
        });
      } else {
        setValidationStatus('invalid');
        toast({
          title: "Invalid Client ID",
          description: "Please check your GitHub Client ID format",
          variant: "destructive",
        });
      }
    } catch (error) {
      setValidationStatus('invalid');
      toast({
        title: "Validation Error",
        description: "Could not validate GitHub Client ID",
        variant: "destructive",
      });
    } finally {
      setIsValidating(false);
    }
  };

  const openGitHubApps = () => {
    window.open('https://github.com/settings/applications/new', '_blank');
  };

  const testOAuthFlow = () => {
    if (!clientId) return;
    
    const state = Math.random().toString(36).substring(2, 15);
    localStorage.setItem('github_oauth_state', state);
    
    const authUrl = `https://github.com/login/oauth/authorize?` +
      `client_id=${clientId}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `scope=repo,user:email&` +
      `state=${state}`;
    
    window.open(authUrl, 'github-oauth-test', 'width=600,height=700');
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Github className="w-6 h-6" />
            <CardTitle>GitHub OAuth Setup</CardTitle>
          </div>
          <CardDescription>
            Configure GitHub OAuth integration to enable repository access and user authentication
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status Badge */}
          <div className="flex items-center space-x-2">
            <Badge variant={isConfigured ? "default" : "secondary"}>
              {isConfigured ? (
                <>
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Configured
                </>
              ) : (
                <>
                  <Settings className="w-3 h-3 mr-1" />
                  Setup Required
                </>
              )}
            </Badge>
            {validationStatus === 'valid' && (
              <Badge variant="outline" className="text-green-600 border-green-600">
                <CheckCircle className="w-3 h-3 mr-1" />
                Valid
              </Badge>
            )}
            {validationStatus === 'invalid' && (
              <Badge variant="outline" className="text-red-600 border-red-600">
                <AlertTriangle className="w-3 h-3 mr-1" />
                Invalid
              </Badge>
            )}
          </div>

          {/* Setup Instructions */}
          {!isConfigured && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                GitHub OAuth is not configured. Follow the steps below to set up GitHub integration.
              </AlertDescription>
            </Alert>
          )}

          {/* Step 1: Create GitHub OAuth App */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                1
              </div>
              <h3 className="text-lg font-semibold">Create GitHub OAuth App</h3>
            </div>
            
            <div className="ml-8 space-y-3">
              <p className="text-sm text-muted-foreground">
                Create a new OAuth App in your GitHub settings with the following configuration:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs font-medium text-muted-foreground">Application Name</Label>
                  <div className="flex items-center space-x-2">
                    <Input 
                      value="Code Alchemy Reactor" 
                      readOnly 
                      className="text-sm"
                    />
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => copyToClipboard("Code Alchemy Reactor", "Application name")}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs font-medium text-muted-foreground">Homepage URL</Label>
                  <div className="flex items-center space-x-2">
                    <Input 
                      value={currentDomain} 
                      readOnly 
                      className="text-sm"
                    />
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => copyToClipboard(currentDomain, "Homepage URL")}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2 md:col-span-2">
                  <Label className="text-xs font-medium text-muted-foreground">Authorization Callback URL</Label>
                  <div className="flex items-center space-x-2">
                    <Input 
                      value={redirectUri} 
                      readOnly 
                      className="text-sm"
                    />
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => copyToClipboard(redirectUri, "Callback URL")}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
              
              <Button onClick={openGitHubApps} className="w-full md:w-auto">
                <ExternalLink className="w-4 h-4 mr-2" />
                Create OAuth App on GitHub
              </Button>
            </div>
          </div>

          <Separator />

          {/* Step 2: Configure Client ID */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                2
              </div>
              <h3 className="text-lg font-semibold">Configure Client ID</h3>
            </div>
            
            <div className="ml-8 space-y-3">
              <p className="text-sm text-muted-foreground">
                Copy the Client ID from your GitHub OAuth App and paste it below:
              </p>
              
              <div className="space-y-2">
                <Label htmlFor="client-id">GitHub Client ID</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="client-id"
                    value={clientId}
                    onChange={(e) => setClientId(e.target.value)}
                    placeholder="Iv1.1234567890abcdef or Ov23li..."
                    className="font-mono text-sm"
                  />
                  <Button 
                    onClick={validateClientId}
                    disabled={isValidating || !clientId.trim()}
                    size="sm"
                  >
                    {isValidating ? (
                      <>
                        <Settings className="w-3 h-3 mr-1 animate-spin" />
                        Validating
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Validate
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              {validationStatus === 'valid' && (
                <Alert>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-600">
                    Client ID format is valid. You can now test the OAuth flow.
                  </AlertDescription>
                </Alert>
              )}
              
              {validationStatus === 'invalid' && (
                <Alert>
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-600">
                    Invalid Client ID format. Please check your GitHub OAuth App settings.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          <Separator />

          {/* Step 3: Test OAuth Flow */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                3
              </div>
              <h3 className="text-lg font-semibold">Test OAuth Flow</h3>
            </div>
            
            <div className="ml-8 space-y-3">
              <p className="text-sm text-muted-foreground">
                Test the GitHub OAuth integration to ensure everything is working correctly:
              </p>
              
              <Button 
                onClick={testOAuthFlow}
                disabled={!isConfigured || validationStatus !== 'valid'}
                className="w-full md:w-auto"
              >
                <Github className="w-4 h-4 mr-2" />
                Test GitHub OAuth
              </Button>
            </div>
          </div>

          {/* Complete Setup */}
          {isConfigured && validationStatus === 'valid' && (
            <div className="pt-4">
              <Button onClick={onComplete} className="w-full">
                <CheckCircle className="w-4 h-4 mr-2" />
                Complete GitHub Setup
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GitHubOAuthSetup;
