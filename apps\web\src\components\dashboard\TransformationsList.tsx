import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Square, 
  CheckCircle, 
  XCircle, 
  Clock, 
  ExternalLink,
  RefreshCw,
  Filter,
  Search,
  MoreHorizontal,
  GitBranch,
  DollarSign
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DashboardWidgetSkeleton } from '@/components/ui/loading-states';
import { formatDistanceToNow } from 'date-fns';

interface Transformation {
  id: string;
  prompt: string;
  status: 'running' | 'completed' | 'failed' | 'timeout';
  final_score?: number;
  iterations_count: number;
  max_iterations: number;
  cost_usd: number;
  execution_time_ms?: number;
  github_pr_url?: string;
  created_at: string;
  completed_at?: string;
}

interface TransformationsListProps {
  data?: Transformation[];
  loading?: boolean;
  onRefresh?: () => void;
  onViewDetails?: (id: string) => void;
  onStop?: (id: string) => void;
  compact?: boolean;
  maxItems?: number;
}

export const TransformationsList: React.FC<TransformationsListProps> = ({
  data = [],
  loading = false,
  onRefresh,
  onViewDetails,
  onStop,
  compact = false,
  maxItems = 10
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showAll, setShowAll] = useState(false);

  if (loading) {
    return <DashboardWidgetSkeleton />;
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="w-4 h-4 text-blue-600 animate-pulse" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed': return <XCircle className="w-4 h-4 text-red-600" />;
      case 'timeout': return <Clock className="w-4 h-4 text-yellow-600" />;
      default: return <MoreHorizontal className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'running': return 'default' as const;
      case 'completed': return 'default' as const;
      case 'failed': return 'destructive' as const;
      case 'timeout': return 'secondary' as const;
      default: return 'outline' as const;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-blue-600';
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'timeout': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3,
    }).format(amount);
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const truncatePrompt = (prompt: string, maxLength: number = 100) => {
    if (prompt.length <= maxLength) return prompt;
    return prompt.substring(0, maxLength) + '...';
  };

  // Filter data
  const filteredData = data.filter(transformation => {
    const matchesSearch = transformation.prompt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || transformation.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const displayData = showAll ? filteredData : filteredData.slice(0, maxItems);

  const runningCount = data.filter(t => t.status === 'running').length;
  const completedCount = data.filter(t => t.status === 'completed').length;
  const failedCount = data.filter(t => t.status === 'failed').length;

  if (compact) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Recent Transformations</h3>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{runningCount} running</Badge>
            <Badge variant="outline">{completedCount} completed</Badge>
            {failedCount > 0 && (
              <Badge variant="destructive">{failedCount} failed</Badge>
            )}
          </div>
        </div>
        <div className="space-y-1">
          {displayData.slice(0, 3).map((transformation) => (
            <div key={transformation.id} className="flex items-center justify-between p-2 bg-muted rounded">
              <div className="flex items-center space-x-2">
                {getStatusIcon(transformation.status)}
                <span className="text-sm truncate max-w-48">
                  {truncatePrompt(transformation.prompt, 50)}
                </span>
              </div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span>{formatCurrency(transformation.cost_usd)}</span>
                <span>{formatDistanceToNow(new Date(transformation.created_at), { addSuffix: true })}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <GitBranch className="w-5 h-5" />
            <CardTitle>Transformations</CardTitle>
            <Badge variant="outline">{data.length} total</Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button size="sm" variant="ghost" onClick={onRefresh}>
              <RefreshCw className="w-3 h-3" />
            </Button>
          </div>
        </div>
        <CardDescription>
          Recent code transformations and their status
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex items-center space-x-2">
          <div className="flex-1">
            <Input
              placeholder="Search transformations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-8"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="running">Running</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
              <SelectItem value="timeout">Timeout</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Status Summary */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-2 bg-blue-50 dark:bg-blue-950 rounded">
            <div className="text-lg font-bold text-blue-600">{runningCount}</div>
            <div className="text-xs text-muted-foreground">Running</div>
          </div>
          <div className="p-2 bg-green-50 dark:bg-green-950 rounded">
            <div className="text-lg font-bold text-green-600">{completedCount}</div>
            <div className="text-xs text-muted-foreground">Completed</div>
          </div>
          <div className="p-2 bg-red-50 dark:bg-red-950 rounded">
            <div className="text-lg font-bold text-red-600">{failedCount}</div>
            <div className="text-xs text-muted-foreground">Failed</div>
          </div>
        </div>

        {/* Transformations List */}
        {displayData.length === 0 ? (
          <div className="text-center py-8">
            <GitBranch className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              {searchTerm || statusFilter !== 'all' ? 'No transformations match your filters' : 'No transformations yet'}
            </p>
          </div>
        ) : (
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {displayData.map((transformation) => (
                <div
                  key={transformation.id}
                  className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-start justify-between space-x-4">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(transformation.status)}
                        <Badge variant={getStatusBadgeVariant(transformation.status)}>
                          {transformation.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(transformation.created_at), { addSuffix: true })}
                        </span>
                      </div>
                      
                      <p className="text-sm font-medium">
                        {truncatePrompt(transformation.prompt)}
                      </p>
                      
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <span>Iterations: {transformation.iterations_count}/{transformation.max_iterations}</span>
                        <span className="flex items-center space-x-1">
                          <DollarSign className="w-3 h-3" />
                          <span>{formatCurrency(transformation.cost_usd)}</span>
                        </span>
                        {transformation.execution_time_ms && (
                          <span>Duration: {formatDuration(transformation.execution_time_ms)}</span>
                        )}
                        {transformation.final_score && (
                          <span>Score: {transformation.final_score}/100</span>
                        )}
                      </div>

                      {transformation.status === 'running' && (
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-xs">
                            <span>Progress</span>
                            <span>{transformation.iterations_count}/{transformation.max_iterations}</span>
                          </div>
                          <Progress 
                            value={(transformation.iterations_count / transformation.max_iterations) * 100}
                            className="h-1"
                          />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {transformation.github_pr_url && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => window.open(transformation.github_pr_url, '_blank')}
                        >
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      )}
                      
                      {transformation.status === 'running' && onStop && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onStop(transformation.id)}
                        >
                          <Square className="w-3 h-3" />
                        </Button>
                      )}
                      
                      {onViewDetails && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onViewDetails(transformation.id)}
                        >
                          View
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}

        {filteredData.length > maxItems && (
          <div className="text-center pt-2 border-t">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAll(!showAll)}
            >
              {showAll ? 'Show less' : `Show all ${filteredData.length} transformations`}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TransformationsList;
