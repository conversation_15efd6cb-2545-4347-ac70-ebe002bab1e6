import React from 'react';
import { cn } from '@/lib/utils';
import { useFormValidation, ValidationRule, FormConfig } from '@/hooks/useFormValidation';
import { InteractiveInput, InteractiveTextarea, ValidationState } from './interactive-feedback';
import { EnhancedButton } from './enhanced-button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { Label } from './label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { Checkbox } from './checkbox';
import { RadioGroup, RadioGroupItem } from './radio-group';
import { AlertTriangle, CheckCircle, Loader2 } from 'lucide-react';

// Field types
export type FieldType = 
  | 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file';

export interface FieldOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface FormField {
  name: string;
  type: FieldType;
  label: string;
  placeholder?: string;
  description?: string;
  options?: FieldOption[]; // For select, radio
  rules?: ValidationRule;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  // Field-specific props
  rows?: number; // For textarea
  accept?: string; // For file input
  multiple?: boolean; // For file input
}

export interface ValidatedFormProps<T extends Record<string, any>> {
  fields: FormField[];
  initialValues: T;
  onSubmit: (values: T) => Promise<void> | void;
  title?: string;
  description?: string;
  submitText?: string;
  resetText?: string;
  showReset?: boolean;
  className?: string;
  cardClassName?: string;
  disabled?: boolean;
  loading?: boolean;
  children?: React.ReactNode; // Custom fields or additional content
}

// Convert field to validation config
const fieldToConfig = (field: FormField): FormConfig<any>[string] => ({
  rules: field.rules,
  validateOnChange: field.validateOnChange ?? true,
  validateOnBlur: field.validateOnBlur ?? true,
  debounceMs: field.debounceMs ?? 300,
});

// Convert form error to validation state
const errorToValidationState = (error: any, touched: boolean): ValidationState | undefined => {
  if (!error || !touched) return undefined;
  
  return {
    isValid: false,
    message: error.message,
    type: 'error',
  };
};

// Field components
const TextField: React.FC<{
  field: FormField;
  fieldProps: any;
  disabled?: boolean;
}> = ({ field, fieldProps, disabled }) => {
  const validation = errorToValidationState(fieldProps.error, fieldProps.touched);
  
  return (
    <InteractiveInput
      type={field.type as any}
      label={field.label}
      placeholder={field.placeholder}
      description={field.description}
      validation={validation}
      disabled={disabled || field.disabled}
      required={field.required}
      className={field.className}
      {...fieldProps}
    />
  );
};

const TextareaField: React.FC<{
  field: FormField;
  fieldProps: any;
  disabled?: boolean;
}> = ({ field, fieldProps, disabled }) => {
  const validation = errorToValidationState(fieldProps.error, fieldProps.touched);
  
  return (
    <InteractiveTextarea
      label={field.label}
      placeholder={field.placeholder}
      description={field.description}
      validation={validation}
      disabled={disabled || field.disabled}
      required={field.required}
      rows={field.rows}
      className={field.className}
      {...fieldProps}
    />
  );
};

const SelectField: React.FC<{
  field: FormField;
  fieldProps: any;
  disabled?: boolean;
}> = ({ field, fieldProps, disabled }) => {
  const hasError = fieldProps.error && fieldProps.touched;
  
  return (
    <div className="space-y-2">
      <Label className={cn(hasError && "text-red-600")}>
        {field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      {field.description && (
        <p className="text-sm text-muted-foreground">{field.description}</p>
      )}
      
      <Select
        value={fieldProps.value}
        onValueChange={(value) => fieldProps.onChange({ target: { value } })}
        disabled={disabled || field.disabled}
      >
        <SelectTrigger className={cn(hasError && "border-red-500")}>
          <SelectValue placeholder={field.placeholder} />
        </SelectTrigger>
        <SelectContent>
          {field.options?.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {hasError && (
        <div className="flex items-center space-x-2 text-sm text-red-600">
          <AlertTriangle className="w-4 h-4" />
          <span>{fieldProps.error.message}</span>
        </div>
      )}
    </div>
  );
};

const CheckboxField: React.FC<{
  field: FormField;
  fieldProps: any;
  disabled?: boolean;
}> = ({ field, fieldProps, disabled }) => {
  const hasError = fieldProps.error && fieldProps.touched;
  
  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <Checkbox
          id={field.name}
          checked={fieldProps.value}
          onCheckedChange={(checked) => 
            fieldProps.onChange({ target: { value: checked } })
          }
          disabled={disabled || field.disabled}
          className={cn(hasError && "border-red-500")}
        />
        <Label
          htmlFor={field.name}
          className={cn("text-sm font-medium", hasError && "text-red-600")}
        >
          {field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      </div>
      
      {field.description && (
        <p className="text-sm text-muted-foreground ml-6">{field.description}</p>
      )}
      
      {hasError && (
        <div className="flex items-center space-x-2 text-sm text-red-600 ml-6">
          <AlertTriangle className="w-4 h-4" />
          <span>{fieldProps.error.message}</span>
        </div>
      )}
    </div>
  );
};

const RadioField: React.FC<{
  field: FormField;
  fieldProps: any;
  disabled?: boolean;
}> = ({ field, fieldProps, disabled }) => {
  const hasError = fieldProps.error && fieldProps.touched;
  
  return (
    <div className="space-y-2">
      <Label className={cn(hasError && "text-red-600")}>
        {field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      {field.description && (
        <p className="text-sm text-muted-foreground">{field.description}</p>
      )}
      
      <RadioGroup
        value={fieldProps.value}
        onValueChange={(value) => fieldProps.onChange({ target: { value } })}
        disabled={disabled || field.disabled}
        className="space-y-2"
      >
        {field.options?.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <RadioGroupItem
              value={option.value}
              id={`${field.name}-${option.value}`}
              disabled={option.disabled}
            />
            <Label
              htmlFor={`${field.name}-${option.value}`}
              className="text-sm font-normal"
            >
              {option.label}
            </Label>
          </div>
        ))}
      </RadioGroup>
      
      {hasError && (
        <div className="flex items-center space-x-2 text-sm text-red-600">
          <AlertTriangle className="w-4 h-4" />
          <span>{fieldProps.error.message}</span>
        </div>
      )}
    </div>
  );
};

// Main form component
export function ValidatedForm<T extends Record<string, any>>({
  fields,
  initialValues,
  onSubmit,
  title,
  description,
  submitText = 'Submit',
  resetText = 'Reset',
  showReset = true,
  className,
  cardClassName,
  disabled = false,
  loading = false,
  children,
}: ValidatedFormProps<T>) {
  // Build form configuration
  const formConfig = React.useMemo(() => {
    return fields.reduce((config, field) => {
      config[field.name] = fieldToConfig(field);
      return config;
    }, {} as FormConfig<T>);
  }, [fields]);

  // Initialize form validation
  const form = useFormValidation(initialValues, formConfig);

  // Handle form submission
  const handleSubmit = form.handleSubmit(async (values) => {
    await onSubmit(values);
  });

  // Render field based on type
  const renderField = (field: FormField) => {
    const fieldProps = form.getFieldProps(field.name as keyof T);
    const isDisabled = disabled || loading || field.disabled;

    switch (field.type) {
      case 'textarea':
        return (
          <TextareaField
            key={field.name}
            field={field}
            fieldProps={fieldProps}
            disabled={isDisabled}
          />
        );
      
      case 'select':
        return (
          <SelectField
            key={field.name}
            field={field}
            fieldProps={fieldProps}
            disabled={isDisabled}
          />
        );
      
      case 'checkbox':
        return (
          <CheckboxField
            key={field.name}
            field={field}
            fieldProps={fieldProps}
            disabled={isDisabled}
          />
        );
      
      case 'radio':
        return (
          <RadioField
            key={field.name}
            field={field}
            fieldProps={fieldProps}
            disabled={isDisabled}
          />
        );
      
      default:
        return (
          <TextField
            key={field.name}
            field={field}
            fieldProps={fieldProps}
            disabled={isDisabled}
          />
        );
    }
  };

  const isFormDisabled = disabled || loading || form.isSubmitting;
  const canSubmit = form.isValid && !isFormDisabled;

  return (
    <Card className={cn("w-full max-w-2xl mx-auto", cardClassName)}>
      {(title || description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      
      <CardContent>
        <form onSubmit={handleSubmit} className={cn("space-y-6", className)}>
          {/* Render fields */}
          <div className="space-y-4">
            {fields.map(renderField)}
          </div>
          
          {/* Custom children */}
          {children}
          
          {/* Form status */}
          {form.submitCount > 0 && !form.isValid && (
            <div className="flex items-center space-x-2 text-sm text-red-600 bg-red-50 p-3 rounded-md">
              <AlertTriangle className="w-4 h-4" />
              <span>Please fix the errors above before submitting.</span>
            </div>
          )}
          
          {form.isValid && form.submitCount > 0 && !form.isSubmitting && (
            <div className="flex items-center space-x-2 text-sm text-green-600 bg-green-50 p-3 rounded-md">
              <CheckCircle className="w-4 h-4" />
              <span>Form is valid and ready to submit.</span>
            </div>
          )}
          
          {/* Form actions */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center space-x-2">
              {showReset && (
                <EnhancedButton
                  type="button"
                  variant="outline"
                  onClick={() => form.resetForm()}
                  disabled={isFormDisabled}
                >
                  {resetText}
                </EnhancedButton>
              )}
            </div>
            
            <EnhancedButton
              type="submit"
              disabled={!canSubmit}
              loading={form.isSubmitting || loading}
              loadingText="Submitting..."
            >
              {form.isSubmitting || loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                submitText
              )}
            </EnhancedButton>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

export default ValidatedForm;
