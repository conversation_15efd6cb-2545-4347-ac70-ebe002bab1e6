import React from 'react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Info, 
  X, 
  Loader2,
  Bell,
  Zap,
  Clock,
  ExternalLink
} from 'lucide-react';
import { Button } from './button';

// Enhanced toast types
export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading' | 'custom';

export interface ToastAction {
  label: string;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'ghost';
}

export interface EnhancedToastOptions {
  type?: ToastType;
  title: string;
  description?: string;
  duration?: number;
  persistent?: boolean;
  actions?: ToastAction[];
  icon?: React.ReactNode;
  sound?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  className?: string;
  onDismiss?: () => void;
  onShow?: () => void;
}

// Toast icons mapping
const getToastIcon = (type: ToastType, customIcon?: React.ReactNode) => {
  if (customIcon) return customIcon;
  
  const iconClass = "w-5 h-5 flex-shrink-0";
  
  switch (type) {
    case 'success':
      return <CheckCircle className={cn(iconClass, "text-green-600")} />;
    case 'error':
      return <XCircle className={cn(iconClass, "text-red-600")} />;
    case 'warning':
      return <AlertTriangle className={cn(iconClass, "text-yellow-600")} />;
    case 'info':
      return <Info className={cn(iconClass, "text-blue-600")} />;
    case 'loading':
      return <Loader2 className={cn(iconClass, "text-blue-600 animate-spin")} />;
    default:
      return <Bell className={cn(iconClass, "text-gray-600")} />;
  }
};

// Toast variant styles
const getToastVariant = (type: ToastType) => {
  switch (type) {
    case 'success':
      return 'border-green-200 bg-green-50 text-green-800';
    case 'error':
      return 'border-red-200 bg-red-50 text-red-800';
    case 'warning':
      return 'border-yellow-200 bg-yellow-50 text-yellow-800';
    case 'info':
      return 'border-blue-200 bg-blue-50 text-blue-800';
    case 'loading':
      return 'border-blue-200 bg-blue-50 text-blue-800';
    default:
      return 'border-gray-200 bg-white text-gray-800';
  }
};

// Enhanced toast hook
export const useEnhancedToast = () => {
  const { toast, dismiss } = useToast();

  const showToast = React.useCallback((options: EnhancedToastOptions) => {
    const {
      type = 'info',
      title,
      description,
      duration = type === 'loading' ? 0 : 5000,
      persistent = false,
      actions = [],
      icon,
      sound = false,
      onDismiss,
      onShow,
      className,
    } = options;

    // Play sound if enabled
    if (sound && typeof window !== 'undefined') {
      try {
        const audio = new Audio('/sounds/notification.mp3');
        audio.volume = 0.3;
        audio.play().catch(() => {
          // Ignore audio play errors (user interaction required)
        });
      } catch (error) {
        // Ignore audio errors
      }
    }

    // Call onShow callback
    onShow?.();

    const toastId = toast({
      title: (
        <div className="flex items-start space-x-3 w-full">
          {getToastIcon(type, icon)}
          <div className="flex-1 min-w-0">
            <div className="font-medium text-sm">{title}</div>
            {description && (
              <div className="text-sm opacity-90 mt-1">{description}</div>
            )}
            {actions.length > 0 && (
              <div className="flex items-center space-x-2 mt-3">
                {actions.map((action, index) => (
                  <Button
                    key={index}
                    size="sm"
                    variant={action.variant || 'outline'}
                    onClick={() => {
                      action.onClick();
                      if (!persistent) {
                        dismiss(toastId);
                      }
                    }}
                    className="h-7 px-2 text-xs"
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            )}
          </div>
        </div>
      ),
      duration: persistent ? 0 : duration,
      className: cn(getToastVariant(type), className),
      onOpenChange: (open) => {
        if (!open) {
          onDismiss?.();
        }
      },
    });

    return toastId;
  }, [toast, dismiss]);

  // Convenience methods
  const success = React.useCallback((title: string, options?: Partial<EnhancedToastOptions>) => {
    return showToast({ ...options, type: 'success', title });
  }, [showToast]);

  const error = React.useCallback((title: string, options?: Partial<EnhancedToastOptions>) => {
    return showToast({ ...options, type: 'error', title });
  }, [showToast]);

  const warning = React.useCallback((title: string, options?: Partial<EnhancedToastOptions>) => {
    return showToast({ ...options, type: 'warning', title });
  }, [showToast]);

  const info = React.useCallback((title: string, options?: Partial<EnhancedToastOptions>) => {
    return showToast({ ...options, type: 'info', title });
  }, [showToast]);

  const loading = React.useCallback((title: string, options?: Partial<EnhancedToastOptions>) => {
    return showToast({ ...options, type: 'loading', title, persistent: true });
  }, [showToast]);

  // Promise-based toast for async operations
  const promise = React.useCallback(async <T,>(
    promise: Promise<T>,
    options: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ): Promise<T> => {
    const loadingToastId = loading(options.loading);

    try {
      const result = await promise;
      dismiss(loadingToastId);
      
      const successMessage = typeof options.success === 'function' 
        ? options.success(result) 
        : options.success;
      
      success(successMessage);
      return result;
    } catch (err) {
      dismiss(loadingToastId);
      
      const errorMessage = typeof options.error === 'function' 
        ? options.error(err) 
        : options.error;
      
      error(errorMessage);
      throw err;
    }
  }, [loading, success, error, dismiss]);

  return {
    toast: showToast,
    success,
    error,
    warning,
    info,
    loading,
    promise,
    dismiss,
  };
};

// Preset toast configurations
export const TOAST_PRESETS = {
  // System notifications
  systemUpdate: (version: string) => ({
    type: 'info' as ToastType,
    title: 'System Update Available',
    description: `Version ${version} is now available`,
    icon: <Zap className="w-5 h-5 text-blue-600" />,
    actions: [
      { label: 'Update Now', onClick: () => window.location.reload() },
      { label: 'Later', onClick: () => {}, variant: 'ghost' as const },
    ],
  }),

  // Transformation notifications
  transformationStarted: (id: string) => ({
    type: 'info' as ToastType,
    title: 'Transformation Started',
    description: `Processing transformation ${id.slice(-8)}...`,
    icon: <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />,
    sound: true,
  }),

  transformationCompleted: (id: string, score: number) => ({
    type: 'success' as ToastType,
    title: 'Transformation Complete',
    description: `Transformation ${id.slice(-8)} completed with score ${score.toFixed(2)}`,
    sound: true,
    actions: [
      { label: 'View Results', onClick: () => window.open(`/transformation/${id}`, '_blank') },
    ],
  }),

  transformationFailed: (id: string, error: string) => ({
    type: 'error' as ToastType,
    title: 'Transformation Failed',
    description: `Transformation ${id.slice(-8)} failed: ${error}`,
    sound: true,
    actions: [
      { label: 'Retry', onClick: () => {}, variant: 'outline' as const },
      { label: 'View Logs', onClick: () => window.open(`/transformation/${id}/logs`, '_blank') },
    ],
  }),

  // Cost notifications
  costAlert: (current: number, limit: number) => ({
    type: 'warning' as ToastType,
    title: 'Cost Alert',
    description: `You've used $${current.toFixed(2)} of your $${limit.toFixed(2)} daily limit`,
    icon: <AlertTriangle className="w-5 h-5 text-yellow-600" />,
    persistent: true,
    actions: [
      { label: 'View Usage', onClick: () => window.open('/monitoring', '_blank') },
      { label: 'Adjust Limits', onClick: () => window.open('/settings', '_blank') },
    ],
  }),

  // GitHub notifications
  prCreated: (url: string, repo: string) => ({
    type: 'success' as ToastType,
    title: 'Pull Request Created',
    description: `Successfully created PR in ${repo}`,
    icon: <ExternalLink className="w-5 h-5 text-green-600" />,
    actions: [
      { label: 'View PR', onClick: () => window.open(url, '_blank') },
    ],
  }),

  // Connection notifications
  connectionLost: () => ({
    type: 'error' as ToastType,
    title: 'Connection Lost',
    description: 'Lost connection to server. Attempting to reconnect...',
    persistent: true,
    icon: <XCircle className="w-5 h-5 text-red-600" />,
  }),

  connectionRestored: () => ({
    type: 'success' as ToastType,
    title: 'Connection Restored',
    description: 'Successfully reconnected to server',
    icon: <CheckCircle className="w-5 h-5 text-green-600" />,
  }),

  // File operations
  fileUploaded: (filename: string) => ({
    type: 'success' as ToastType,
    title: 'File Uploaded',
    description: `Successfully uploaded ${filename}`,
  }),

  fileUploadFailed: (filename: string, error: string) => ({
    type: 'error' as ToastType,
    title: 'Upload Failed',
    description: `Failed to upload ${filename}: ${error}`,
    actions: [
      { label: 'Retry', onClick: () => {}, variant: 'outline' as const },
    ],
  }),

  // Settings notifications
  settingsSaved: () => ({
    type: 'success' as ToastType,
    title: 'Settings Saved',
    description: 'Your preferences have been updated successfully',
  }),

  settingsError: (error: string) => ({
    type: 'error' as ToastType,
    title: 'Settings Error',
    description: `Failed to save settings: ${error}`,
    actions: [
      { label: 'Retry', onClick: () => {}, variant: 'outline' as const },
    ],
  }),
};

// Toast notification manager component
export const ToastManager: React.FC = () => {
  const enhancedToast = useEnhancedToast();

  // Listen for global events and show appropriate toasts
  React.useEffect(() => {
    const handleOnline = () => {
      enhancedToast.success('Back Online', {
        description: 'Internet connection restored',
        icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      });
    };

    const handleOffline = () => {
      enhancedToast.error('Connection Lost', {
        description: 'No internet connection detected',
        icon: <XCircle className="w-5 h-5 text-red-600" />,
        persistent: true,
      });
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // User returned to tab - could check for updates
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [enhancedToast]);

  return null;
};

export default useEnhancedToast;
