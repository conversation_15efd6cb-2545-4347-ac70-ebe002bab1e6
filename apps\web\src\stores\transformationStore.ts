import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types
export interface AgentLog {
  id: string;
  iteration: number;
  plan: string;
  critique: string;
  score: number;
  patch?: any;
  tokens_used: number;
  cost_usd: number;
  timestamp: Date;
}

export interface TransformationJob {
  id: string;
  prompt: string;
  originalCode: string;
  transformedCode: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: number;
  iterations: number;
  maxIterations: number;
  currentScore: number;
  targetScore: number;
  finalScore?: number;
  cost: number;
  startTime: Date | null;
  endTime: Date | null;
  estimatedTimeRemaining?: number;
  agentLogs: AgentLog[];
  githubPrUrl?: string;
  error?: string;
  metadata: {
    language: string;
    plannerModel: string;
    criticModel: string;
    userId: string;
  };
}

export interface TransformationQueue {
  jobs: TransformationJob[];
  activeJobId: string | null;
  maxConcurrentJobs: number;
  totalProcessed: number;
  totalCost: number;
  averageScore: number;
  successRate: number;
}

export interface TransformationState {
  queue: TransformationQueue;
  history: TransformationJob[];
  currentJob: TransformationJob | null;
  isProcessing: boolean;
  lastUpdate: Date | null;
}

export interface TransformationActions {
  // Queue management
  addJob: (job: Omit<TransformationJob, 'id' | 'status' | 'startTime' | 'endTime' | 'agentLogs'>) => string;
  removeJob: (jobId: string) => void;
  clearQueue: () => void;
  reorderJobs: (jobIds: string[]) => void;
  
  // Job control
  startJob: (jobId: string) => void;
  pauseJob: (jobId: string) => void;
  cancelJob: (jobId: string) => void;
  retryJob: (jobId: string) => void;
  
  // Progress updates
  updateJobProgress: (jobId: string, updates: Partial<TransformationJob>) => void;
  addAgentLog: (jobId: string, log: Omit<AgentLog, 'id' | 'timestamp'>) => void;
  updateJobStatus: (jobId: string, status: TransformationJob['status']) => void;
  
  // Statistics
  calculateStats: () => void;
  getJobsByStatus: (status: TransformationJob['status']) => TransformationJob[];
  getRecentJobs: (limit?: number) => TransformationJob[];
  
  // Utilities
  exportHistory: () => TransformationJob[];
  importHistory: (jobs: TransformationJob[]) => void;
  clearHistory: () => void;
  resetStore: () => void;
}

// Initial state
const initialState: TransformationState = {
  queue: {
    jobs: [],
    activeJobId: null,
    maxConcurrentJobs: 1,
    totalProcessed: 0,
    totalCost: 0,
    averageScore: 0,
    successRate: 0,
  },
  history: [],
  currentJob: null,
  isProcessing: false,
  lastUpdate: null,
};

// Utility functions
const generateJobId = () => `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
const generateLogId = () => `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const calculateEstimatedTime = (job: TransformationJob): number | undefined => {
  if (job.iterations === 0 || !job.startTime) return undefined;
  
  const elapsedTime = Date.now() - job.startTime.getTime();
  const avgTimePerIteration = elapsedTime / job.iterations;
  const remainingIterations = job.maxIterations - job.iterations;
  
  return avgTimePerIteration * remainingIterations;
};

// Create the store
export const useTransformationStore = create<TransformationState & TransformationActions>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,
      
      // Queue management
      addJob: (jobData) => {
        const jobId = generateJobId();
        const newJob: TransformationJob = {
          ...jobData,
          id: jobId,
          status: 'queued',
          startTime: null,
          endTime: null,
          agentLogs: [],
        };
        
        set((state) => {
          // Insert job based on priority (higher priority first)
          const insertIndex = state.queue.jobs.findIndex(job => job.priority < newJob.priority);
          if (insertIndex === -1) {
            state.queue.jobs.push(newJob);
          } else {
            state.queue.jobs.splice(insertIndex, 0, newJob);
          }
          state.lastUpdate = new Date();
        });
        
        return jobId;
      },
      
      removeJob: (jobId) => set((state) => {
        const jobIndex = state.queue.jobs.findIndex(job => job.id === jobId);
        if (jobIndex !== -1) {
          const job = state.queue.jobs[jobIndex];
          if (job.status === 'running') {
            job.status = 'cancelled';
            job.endTime = new Date();
          }
          state.queue.jobs.splice(jobIndex, 1);
          
          if (state.queue.activeJobId === jobId) {
            state.queue.activeJobId = null;
            state.currentJob = null;
            state.isProcessing = false;
          }
          
          state.lastUpdate = new Date();
        }
      }),
      
      clearQueue: () => set((state) => {
        // Cancel running jobs
        state.queue.jobs.forEach(job => {
          if (job.status === 'running') {
            job.status = 'cancelled';
            job.endTime = new Date();
          }
        });
        
        state.queue.jobs = [];
        state.queue.activeJobId = null;
        state.currentJob = null;
        state.isProcessing = false;
        state.lastUpdate = new Date();
      }),
      
      reorderJobs: (jobIds) => set((state) => {
        const jobMap = new Map(state.queue.jobs.map(job => [job.id, job]));
        const reorderedJobs = jobIds
          .map(id => jobMap.get(id))
          .filter(Boolean) as TransformationJob[];
        
        state.queue.jobs = reorderedJobs;
        state.lastUpdate = new Date();
      }),
      
      // Job control
      startJob: (jobId) => set((state) => {
        const job = state.queue.jobs.find(j => j.id === jobId);
        if (job && job.status === 'queued') {
          job.status = 'running';
          job.startTime = new Date();
          job.endTime = null;
          
          state.queue.activeJobId = jobId;
          state.currentJob = job;
          state.isProcessing = true;
          state.lastUpdate = new Date();
        }
      }),
      
      pauseJob: (jobId) => set((state) => {
        const job = state.queue.jobs.find(j => j.id === jobId);
        if (job && job.status === 'running') {
          job.status = 'queued';
          
          if (state.queue.activeJobId === jobId) {
            state.queue.activeJobId = null;
            state.currentJob = null;
            state.isProcessing = false;
          }
          
          state.lastUpdate = new Date();
        }
      }),
      
      cancelJob: (jobId) => set((state) => {
        const job = state.queue.jobs.find(j => j.id === jobId);
        if (job && (job.status === 'running' || job.status === 'queued')) {
          job.status = 'cancelled';
          job.endTime = new Date();
          
          if (state.queue.activeJobId === jobId) {
            state.queue.activeJobId = null;
            state.currentJob = null;
            state.isProcessing = false;
          }
          
          state.lastUpdate = new Date();
        }
      }),
      
      retryJob: (jobId) => set((state) => {
        const job = state.queue.jobs.find(j => j.id === jobId);
        if (job && (job.status === 'failed' || job.status === 'cancelled')) {
          job.status = 'queued';
          job.startTime = null;
          job.endTime = null;
          job.iterations = 0;
          job.currentScore = 0;
          job.cost = 0;
          job.agentLogs = [];
          job.error = undefined;
          
          state.lastUpdate = new Date();
        }
      }),
      
      // Progress updates
      updateJobProgress: (jobId, updates) => set((state) => {
        const job = state.queue.jobs.find(j => j.id === jobId);
        if (job) {
          Object.assign(job, updates);
          
          // Calculate estimated time remaining
          if (job.status === 'running') {
            job.estimatedTimeRemaining = calculateEstimatedTime(job);
          }
          
          // Update current job if it's the active one
          if (state.queue.activeJobId === jobId) {
            state.currentJob = job;
          }
          
          state.lastUpdate = new Date();
        }
      }),
      
      addAgentLog: (jobId, logData) => set((state) => {
        const job = state.queue.jobs.find(j => j.id === jobId);
        if (job) {
          const newLog: AgentLog = {
            ...logData,
            id: generateLogId(),
            timestamp: new Date(),
          };
          
          job.agentLogs.push(newLog);
          job.iterations = Math.max(job.iterations, newLog.iteration);
          job.currentScore = newLog.score;
          job.cost += newLog.cost_usd;
          
          // Update current job if it's the active one
          if (state.queue.activeJobId === jobId) {
            state.currentJob = job;
          }
          
          state.lastUpdate = new Date();
        }
      }),
      
      updateJobStatus: (jobId, status) => set((state) => {
        const job = state.queue.jobs.find(j => j.id === jobId);
        if (job) {
          const previousStatus = job.status;
          job.status = status;
          
          if (status === 'completed' || status === 'failed' || status === 'cancelled') {
            job.endTime = new Date();
            
            // Move to history
            state.history.unshift({ ...job });
            
            // Remove from queue
            const jobIndex = state.queue.jobs.findIndex(j => j.id === jobId);
            if (jobIndex !== -1) {
              state.queue.jobs.splice(jobIndex, 1);
            }
            
            // Update queue stats
            if (status === 'completed' || status === 'failed') {
              state.queue.totalProcessed += 1;
              state.queue.totalCost += job.cost;
            }
            
            // Clear active job if this was it
            if (state.queue.activeJobId === jobId) {
              state.queue.activeJobId = null;
              state.currentJob = null;
              state.isProcessing = false;
            }
          }
          
          state.lastUpdate = new Date();
        }
      }),
      
      // Statistics
      calculateStats: () => set((state) => {
        const completedJobs = state.history.filter(job => job.status === 'completed');
        const totalJobs = state.history.length;
        
        if (completedJobs.length > 0) {
          state.queue.averageScore = completedJobs.reduce((sum, job) => sum + (job.finalScore || 0), 0) / completedJobs.length;
        }
        
        if (totalJobs > 0) {
          state.queue.successRate = (completedJobs.length / totalJobs) * 100;
        }
      }),
      
      getJobsByStatus: (status) => {
        const state = get();
        return state.queue.jobs.filter(job => job.status === status);
      },
      
      getRecentJobs: (limit = 10) => {
        const state = get();
        return state.history.slice(0, limit);
      },
      
      // Utilities
      exportHistory: () => {
        const state = get();
        return state.history;
      },
      
      importHistory: (jobs) => set((state) => {
        state.history = jobs;
        state.lastUpdate = new Date();
      }),
      
      clearHistory: () => set((state) => {
        state.history = [];
        state.queue.totalProcessed = 0;
        state.queue.totalCost = 0;
        state.queue.averageScore = 0;
        state.queue.successRate = 0;
        state.lastUpdate = new Date();
      }),
      
      resetStore: () => set(() => initialState),
    }))
  )
);

// Selectors for optimized subscriptions
export const selectActiveJob = (state: TransformationState & TransformationActions) => state.currentJob;
export const selectQueueLength = (state: TransformationState & TransformationActions) => state.queue.jobs.length;
export const selectIsProcessing = (state: TransformationState & TransformationActions) => state.isProcessing;
export const selectQueueStats = (state: TransformationState & TransformationActions) => ({
  totalProcessed: state.queue.totalProcessed,
  totalCost: state.queue.totalCost,
  averageScore: state.queue.averageScore,
  successRate: state.queue.successRate,
});

// Hook for subscribing to specific job updates
export const useJobUpdates = (jobId: string | null) => {
  return useTransformationStore(
    (state) => jobId ? state.queue.jobs.find(job => job.id === jobId) || state.history.find(job => job.id === jobId) : null
  );
};
