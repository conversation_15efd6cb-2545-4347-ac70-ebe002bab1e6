import { test, expect } from '@playwright/test';

test.describe('Authentication Flow E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing auth state
    await page.addInitScript(() => {
      window.localStorage.clear();
      window.sessionStorage.clear();
    });
  });

  test('should handle unauthenticated state correctly', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Should show authentication prompt or redirect to login
    await expect(page.locator('text=Sign in')).toBeVisible({ timeout: 5000 });
    
    // Should not show authenticated features
    await expect(page.locator('text=Start Reactor')).not.toBeVisible();
  });

  test('should authenticate with Supabase successfully', async ({ page }) => {
    // Mock successful Supabase auth
    await page.route('**/auth/v1/token**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock-access-token',
          token_type: 'bearer',
          expires_in: 3600,
          refresh_token: 'mock-refresh-token',
          user: {
            id: 'test-user-123',
            email: '<EMAIL>',
            email_confirmed_at: new Date().toISOString(),
            app_metadata: { role: 'authenticated' },
            user_metadata: { name: 'Test User' }
          }
        })
      });
    });

    await page.goto('/dashboard');
    
    // Click sign in button
    await page.click('text=Sign in');
    
    // Fill in credentials (mock form)
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword');
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard with authenticated state
    await expect(page.locator('text=Start Reactor')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
  });

  test('should handle authentication errors gracefully', async ({ page }) => {
    // Mock auth error
    await page.route('**/auth/v1/token**', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'invalid_credentials',
          error_description: 'Invalid email or password'
        })
      });
    });

    await page.goto('/dashboard');
    
    // Try to sign in with invalid credentials
    await page.click('text=Sign in');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    // Should show error message
    await expect(page.locator('text=Invalid email or password')).toBeVisible();
    
    // Should remain on login page
    await expect(page.locator('text=Sign in')).toBeVisible();
  });

  test('should handle token refresh automatically', async ({ page }) => {
    // Set up initial auth state with expired token
    await page.addInitScript(() => {
      const expiredToken = {
        access_token: 'expired-token',
        user: {
          id: 'test-user-123',
          email: '<EMAIL>'
        },
        expires_at: Date.now() / 1000 - 3600 // Expired 1 hour ago
      };
      window.localStorage.setItem('supabase.auth.token', JSON.stringify(expiredToken));
    });

    // Mock refresh token endpoint
    await page.route('**/auth/v1/token**', async route => {
      if (route.request().postData()?.includes('refresh_token')) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            access_token: 'new-access-token',
            token_type: 'bearer',
            expires_in: 3600,
            refresh_token: 'new-refresh-token',
            user: {
              id: 'test-user-123',
              email: '<EMAIL>'
            }
          })
        });
      }
    });

    await page.goto('/dashboard');
    
    // Should automatically refresh token and show authenticated state
    await expect(page.locator('text=Start Reactor')).toBeVisible({ timeout: 10000 });
  });

  test('should sign out successfully', async ({ page }) => {
    // Set up authenticated state
    await page.addInitScript(() => {
      const authToken = {
        access_token: 'valid-token',
        user: {
          id: 'test-user-123',
          email: '<EMAIL>'
        },
        expires_at: Date.now() / 1000 + 3600
      };
      window.localStorage.setItem('supabase.auth.token', JSON.stringify(authToken));
    });

    // Mock sign out endpoint
    await page.route('**/auth/v1/logout', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({})
      });
    });

    await page.goto('/dashboard');
    
    // Should be authenticated initially
    await expect(page.locator('text=Start Reactor')).toBeVisible();
    
    // Click user menu and sign out
    await page.click('[data-testid="user-menu"]');
    await page.click('text=Sign out');
    
    // Should redirect to login page
    await expect(page.locator('text=Sign in')).toBeVisible({ timeout: 5000 });
    
    // Should clear auth state
    const authToken = await page.evaluate(() => 
      window.localStorage.getItem('supabase.auth.token')
    );
    expect(authToken).toBeNull();
  });

  test('should handle session persistence across page reloads', async ({ page }) => {
    // Set up authenticated state
    await page.addInitScript(() => {
      const authToken = {
        access_token: 'valid-token',
        user: {
          id: 'test-user-123',
          email: '<EMAIL>',
          name: 'Test User'
        },
        expires_at: Date.now() / 1000 + 3600
      };
      window.localStorage.setItem('supabase.auth.token', JSON.stringify(authToken));
    });

    await page.goto('/dashboard');
    
    // Should be authenticated
    await expect(page.locator('text=Start Reactor')).toBeVisible();
    
    // Reload page
    await page.reload();
    
    // Should still be authenticated
    await expect(page.locator('text=Start Reactor')).toBeVisible();
    await expect(page.locator('text=Test User')).toBeVisible();
  });

  test('should protect authenticated routes', async ({ page }) => {
    // Try to access protected route without authentication
    await page.goto('/settings');
    
    // Should redirect to login or show auth prompt
    await expect(page.locator('text=Sign in')).toBeVisible({ timeout: 5000 });
    
    // URL should be redirected or show login state
    const url = page.url();
    expect(url).toMatch(/(login|auth|signin)/);
  });

  test('should handle network errors during authentication', async ({ page }) => {
    // Mock network error
    await page.route('**/auth/v1/token**', async route => {
      await route.abort('failed');
    });

    await page.goto('/dashboard');
    
    // Try to sign in
    await page.click('text=Sign in');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword');
    await page.click('button[type="submit"]');
    
    // Should show network error message
    await expect(page.locator('text=Network error')).toBeVisible();
  });
});
