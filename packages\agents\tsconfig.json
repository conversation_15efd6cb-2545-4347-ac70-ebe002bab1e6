{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": false, "noImplicitAny": false, "strictNullChecks": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}