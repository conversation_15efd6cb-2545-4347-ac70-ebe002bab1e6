<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <title>Metamorphic Reactor System Architecture</title>
  
  <!-- Frontend Layer -->
  <rect x="50" y="50" width="300" height="120" fill="#e1f5fe" stroke="#01579b" stroke-width="2" rx="10"/>
  <text x="200" y="80" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">Frontend Layer</text>
  <text x="200" y="100" text-anchor="middle" font-family="Arial" font-size="12">React/Vite (Port: 8080)</text>
  <text x="200" y="120" text-anchor="middle" font-family="Arial" font-size="12">Monaco Editor + shadcn/ui</text>
  <text x="200" y="140" text-anchor="middle" font-family="Arial" font-size="12">Tailwind CSS</text>
  
  <!-- API Layer -->
  <rect x="50" y="200" width="300" height="120" fill="#f3e5f5" stroke="#4a148c" stroke-width="2" rx="10"/>
  <text x="200" y="230" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">API Layer</text>
  <text x="200" y="250" text-anchor="middle" font-family="Arial" font-size="12">Express.js (Port: 3001)</text>
  <text x="200" y="270" text-anchor="middle" font-family="Arial" font-size="12">JWT Auth + Rate Limiting</text>
  <text x="200" y="290" text-anchor="middle" font-family="Arial" font-size="12">Security Middleware</text>
  
  <!-- Dual Agent System -->
  <rect x="400" y="50" width="350" height="270" fill="#e8f5e8" stroke="#1b5e20" stroke-width="2" rx="10"/>
  <text x="575" y="80" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">Dual Agent System</text>
  
  <!-- Plan Agent -->
  <rect x="420" y="100" width="140" height="60" fill="#c8e6c9" stroke="#2e7d32" stroke-width="1" rx="5"/>
  <text x="490" y="125" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">Plan Agent</text>
  <text x="490" y="145" text-anchor="middle" font-family="Arial" font-size="10">JSON Patch Generation</text>
  
  <!-- Critique Agent -->
  <rect x="580" y="100" width="140" height="60" fill="#c8e6c9" stroke="#2e7d32" stroke-width="1" rx="5"/>
  <text x="650" y="125" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">Critique Agent</text>
  <text x="650" y="145" text-anchor="middle" font-family="Arial" font-size="10">Patch Evaluation</text>
  
  <!-- Provider Layer -->
  <rect x="420" y="180" width="300" height="60" fill="#dcedc8" stroke="#33691e" stroke-width="1" rx="5"/>
  <text x="570" y="200" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">Provider Layer</text>
  <text x="570" y="220" text-anchor="middle" font-family="Arial" font-size="10">OpenAI | Anthropic | Vertex AI</text>
  
  <!-- Utilities -->
  <rect x="420" y="260" width="300" height="50" fill="#f1f8e9" stroke="#558b2f" stroke-width="1" rx="5"/>
  <text x="570" y="280" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">Utilities</text>
  <text x="570" y="295" text-anchor="middle" font-family="Arial" font-size="10">Cost Guard | Token Monitor | Failover | Streaming</text>
  
  <!-- Data Layer -->
  <rect x="50" y="350" width="300" height="100" fill="#fff3e0" stroke="#e65100" stroke-width="2" rx="10"/>
  <text x="200" y="380" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">Data Layer</text>
  <text x="200" y="400" text-anchor="middle" font-family="Arial" font-size="12">Supabase PostgreSQL</text>
  <text x="200" y="420" text-anchor="middle" font-family="Arial" font-size="12">Redis Cache</text>
  
  <!-- External Services -->
  <rect x="800" y="50" width="300" height="120" fill="#fce4ec" stroke="#880e4f" stroke-width="2" rx="10"/>
  <text x="950" y="80" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">External Services</text>
  <text x="950" y="100" text-anchor="middle" font-family="Arial" font-size="12">GitHub API</text>
  <text x="950" y="120" text-anchor="middle" font-family="Arial" font-size="12">AI Provider APIs</text>
  <text x="950" y="140" text-anchor="middle" font-family="Arial" font-size="12">OpenAI/Anthropic/Google</text>
  
  <!-- Infrastructure -->
  <rect x="800" y="200" width="300" height="120" fill="#f1f8e9" stroke="#33691e" stroke-width="2" rx="10"/>
  <text x="950" y="230" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">Infrastructure</text>
  <text x="950" y="250" text-anchor="middle" font-family="Arial" font-size="12">Docker Containers</text>
  <text x="950" y="270" text-anchor="middle" font-family="Arial" font-size="12">Nginx Reverse Proxy</text>
  <text x="950" y="290" text-anchor="middle" font-family="Arial" font-size="12">Monitoring Stack</text>
  
  <!-- CI/CD Pipeline -->
  <rect x="800" y="350" width="300" height="120" fill="#e0f2f1" stroke="#004d40" stroke-width="2" rx="10"/>
  <text x="950" y="380" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">CI/CD Pipeline</text>
  <text x="950" y="400" text-anchor="middle" font-family="Arial" font-size="12">GitHub Actions (12 workflows)</text>
  <text x="950" y="420" text-anchor="middle" font-family="Arial" font-size="12">Quality Gates</text>
  <text x="950" y="440" text-anchor="middle" font-family="Arial" font-size="12">Security Scanning</text>
  
  <!-- Connections -->
  <!-- Frontend to API -->
  <line x1="200" y1="170" x2="200" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- API to Agents -->
  <line x1="350" y1="260" x2="400" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Agents to Providers -->
  <line x1="575" y1="160" x2="575" y2="180" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- API to Data -->
  <line x1="200" y1="320" x2="200" y2="350" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Agents to External -->
  <line x1="750" y1="200" x2="800" y2="110" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Infrastructure connections -->
  <line x1="350" y1="110" x2="800" y2="260" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- CI/CD connections -->
  <line x1="575" y1="320" x2="800" y2="410" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
  
  <!-- Legend -->
  <rect x="50" y="500" width="1100" height="250" fill="#f5f5f5" stroke="#ccc" stroke-width="1" rx="5"/>
  <text x="600" y="530" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">System Architecture Legend</text>
  
  <!-- Legend items -->
  <rect x="80" y="550" width="20" height="15" fill="#e1f5fe"/>
  <text x="110" y="562" font-family="Arial" font-size="12">Frontend Layer</text>
  
  <rect x="250" y="550" width="20" height="15" fill="#f3e5f5"/>
  <text x="280" y="562" font-family="Arial" font-size="12">API Layer</text>
  
  <rect x="400" y="550" width="20" height="15" fill="#e8f5e8"/>
  <text x="430" y="562" font-family="Arial" font-size="12">Dual Agent System</text>
  
  <rect x="580" y="550" width="20" height="15" fill="#fff3e0"/>
  <text x="610" y="562" font-family="Arial" font-size="12">Data Layer</text>
  
  <rect x="80" y="580" width="20" height="15" fill="#fce4ec"/>
  <text x="110" y="592" font-family="Arial" font-size="12">External Services</text>
  
  <rect x="250" y="580" width="20" height="15" fill="#f1f8e9"/>
  <text x="280" y="592" font-family="Arial" font-size="12">Infrastructure</text>
  
  <rect x="400" y="580" width="20" height="15" fill="#e0f2f1"/>
  <text x="430" y="592" font-family="Arial" font-size="12">CI/CD Pipeline</text>
  
  <!-- Key Features -->
  <text x="80" y="630" font-family="Arial" font-size="14" font-weight="bold">Key Features:</text>
  <text x="80" y="650" font-family="Arial" font-size="12">• Multi-provider AI integration (OpenAI, Anthropic, Vertex AI)</text>
  <text x="80" y="670" font-family="Arial" font-size="12">• Cost management with budget controls ($0.25 planning, $0.15 critique)</text>
  <text x="80" y="690" font-family="Arial" font-size="12">• Advanced security with JWT auth, rate limiting, and CSP headers</text>
  <text x="80" y="710" font-family="Arial" font-size="12">• Real-time streaming with Server-Sent Events</text>
  <text x="80" y="730" font-family="Arial" font-size="12">• Production-ready infrastructure with Docker and monitoring</text>
</svg>
