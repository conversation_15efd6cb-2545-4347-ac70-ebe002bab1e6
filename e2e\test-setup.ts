import { Page } from '@playwright/test';

/**
 * Test utilities for E2E tests
 */

export interface TestUser {
  id: string;
  email: string;
  name: string;
  accessToken: string;
}

export interface GitHubUser {
  login: string;
  name: string;
  email: string;
  avatar_url: string;
}

/**
 * Set up authenticated Supabase state
 */
export async function setupSupabaseAuth(page: Page, user?: Partial<TestUser>) {
  const defaultUser: TestUser = {
    id: 'test-user-123',
    email: '<EMAIL>',
    name: 'Test User',
    accessToken: 'mock-access-token',
    ...user
  };

  await page.addInitScript((userData) => {
    const authToken = {
      access_token: userData.accessToken,
      user: {
        id: userData.id,
        email: userData.email,
        name: userData.name
      },
      expires_at: Date.now() / 1000 + 3600 // 1 hour from now
    };
    window.localStorage.setItem('supabase.auth.token', JSON.stringify(authToken));
  }, defaultUser);

  return defaultUser;
}

/**
 * Set up GitHub OAuth mocks
 */
export async function setupGitHubMocks(page: Page, user?: Partial<GitHubUser>, connected = true) {
  const defaultUser: GitHubUser = {
    login: 'testuser',
    name: 'Test User',
    email: '<EMAIL>',
    avatar_url: 'https://avatars.githubusercontent.com/u/123456?v=4',
    ...user
  };

  // Mock GitHub OAuth status
  await page.route('**/functions/v1/github-oauth', async route => {
    const method = route.request().method();
    
    if (method === 'GET') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          hasToken: connected,
          tokenInfo: connected ? defaultUser : null
        })
      });
    } else if (method === 'POST') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: defaultUser
        })
      });
    } else if (method === 'DELETE') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    }
  });

  // Mock GitHub API
  await page.route('**/api.github.com/user', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(defaultUser)
    });
  });

  await page.route('**/api.github.com/user/repos', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([
        {
          id: 1,
          name: 'test-repo',
          full_name: `${defaultUser.login}/test-repo`,
          private: false,
          html_url: `https://github.com/${defaultUser.login}/test-repo`,
          description: 'A test repository',
          default_branch: 'main',
          permissions: { admin: true, push: true, pull: true }
        }
      ])
    });
  });

  return defaultUser;
}

/**
 * Mock successful reactor loop
 */
export async function mockReactorLoop(page: Page, options?: {
  finalScore?: number;
  iterations?: number;
  patchDescription?: string;
}) {
  const {
    finalScore = 0.92,
    iterations = 3,
    patchDescription = 'Optimized code for better performance'
  } = options || {};

  await page.route('**/functions/v1/ai-loop', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        finalPatch: {
          operations: [
            { op: 'replace', path: '/function', value: 'optimized function code' }
          ],
          description: patchDescription
        },
        finalScore,
        iterations,
        logs: Array.from({ length: iterations }, (_, i) => ({
          iteration: i + 1,
          plan: `Optimization step ${i + 1}`,
          critique: `Iteration ${i + 1} feedback`,
          score: (finalScore * (i + 1)) / iterations
        })),
        completed: true
      })
    });
  });
}

/**
 * Mock GitHub PR creation
 */
export async function mockGitHubPR(page: Page, prNumber = 42) {
  await page.route('**/functions/v1/create-pr', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        pr: {
          url: `https://github.com/testuser/test-repo/pull/${prNumber}`,
          number: prNumber,
          branch: `reactor/auto-optimization-${prNumber}`,
          title: 'feat: reactor auto-optimization'
        }
      })
    });
  });
}

/**
 * Clear all browser state
 */
export async function clearBrowserState(page: Page) {
  await page.addInitScript(() => {
    window.localStorage.clear();
    window.sessionStorage.clear();
  });
}

/**
 * Wait for Monaco Editor to be ready
 */
export async function waitForMonacoEditor(page: Page) {
  await page.waitForSelector('.monaco-editor', { timeout: 10000 });
  // Wait for Monaco to be fully initialized
  await page.waitForFunction(() => {
    const editor = document.querySelector('.monaco-editor');
    return editor && window.monaco;
  });
}

/**
 * Type code into Monaco Editor
 */
export async function typeInMonacoEditor(page: Page, code: string) {
  await waitForMonacoEditor(page);
  const editor = page.locator('.monaco-editor');
  await editor.click();
  await page.keyboard.press('Control+A');
  await page.keyboard.type(code);
}

/**
 * Start reactor loop and wait for completion
 */
export async function runReactorLoop(page: Page, code: string) {
  await typeInMonacoEditor(page, code);
  await page.click('text=Start');
  await page.waitForSelector('text=Running', { timeout: 5000 });
  await page.waitForSelector('text=Reactor loop completed', { timeout: 15000 });
}

/**
 * Create a GitHub PR through the UI
 */
export async function createGitHubPR(page: Page, options?: {
  title?: string;
  description?: string;
  repository?: string;
}) {
  const {
    title = 'feat: reactor optimization',
    description = 'Automated optimization via Metamorphic Reactor',
    repository = 'testuser/test-repo'
  } = options || {};

  await page.click('text=Create PR');
  await page.fill('input[placeholder*="title"]', title);
  await page.fill('textarea[placeholder*="description"]', description);
  await page.selectOption('select[name="repository"]', repository);
  await page.click('button:has-text("Create Pull Request")');
  
  await page.waitForSelector('text=Pull Request created successfully', { timeout: 10000 });
}

/**
 * Assert that quality gates are met
 */
export async function assertQualityGates(page: Page) {
  // Check that accessibility features are present
  await page.waitForSelector('[aria-label]', { timeout: 5000 });
  
  // Check that the page is responsive
  await page.setViewportSize({ width: 375, height: 667 }); // Mobile
  await page.waitForSelector('text=Dashboard', { timeout: 5000 });
  
  await page.setViewportSize({ width: 1280, height: 720 }); // Desktop
  await page.waitForSelector('text=Dashboard', { timeout: 5000 });
}
