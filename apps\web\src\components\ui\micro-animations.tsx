import React from 'react';
import { cn } from '@/lib/utils';

// Animation types
export type AnimationType = 
  | 'fadeIn' | 'fadeOut' | 'slideUp' | 'slideDown' | 'slideLeft' | 'slideRight'
  | 'scaleIn' | 'scaleOut' | 'bounce' | 'pulse' | 'shake' | 'wobble'
  | 'flipX' | 'flipY' | 'rotateIn' | 'rotateOut' | 'zoomIn' | 'zoomOut';

export type AnimationDuration = 'fast' | 'normal' | 'slow' | 'slower';
export type AnimationEasing = 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'bounce';

// Animation configuration
export interface AnimationConfig {
  type: AnimationType;
  duration?: AnimationDuration;
  easing?: AnimationEasing;
  delay?: number;
  repeat?: number | 'infinite';
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
}

// Component props
export interface AnimatedElementProps {
  children: React.ReactNode;
  animation: AnimationConfig;
  trigger?: 'mount' | 'hover' | 'focus' | 'click' | 'visible' | 'manual';
  isActive?: boolean;
  onAnimationStart?: () => void;
  onAnimationEnd?: () => void;
  className?: string;
}

// Animation keyframes and classes
const animationClasses = {
  // Fade animations
  fadeIn: 'animate-fade-in',
  fadeOut: 'animate-fade-out',
  
  // Slide animations
  slideUp: 'animate-slide-up',
  slideDown: 'animate-slide-down',
  slideLeft: 'animate-slide-left',
  slideRight: 'animate-slide-right',
  
  // Scale animations
  scaleIn: 'animate-scale-in',
  scaleOut: 'animate-scale-out',
  
  // Attention seekers
  bounce: 'animate-bounce',
  pulse: 'animate-pulse',
  shake: 'animate-shake',
  wobble: 'animate-wobble',
  
  // Flip animations
  flipX: 'animate-flip-x',
  flipY: 'animate-flip-y',
  
  // Rotate animations
  rotateIn: 'animate-rotate-in',
  rotateOut: 'animate-rotate-out',
  
  // Zoom animations
  zoomIn: 'animate-zoom-in',
  zoomOut: 'animate-zoom-out',
};

const durationClasses = {
  fast: 'duration-150',
  normal: 'duration-300',
  slow: 'duration-500',
  slower: 'duration-700',
};

const easingClasses = {
  linear: 'ease-linear',
  ease: 'ease',
  'ease-in': 'ease-in',
  'ease-out': 'ease-out',
  'ease-in-out': 'ease-in-out',
  bounce: 'ease-bounce',
};

// Intersection Observer hook for visibility trigger
const useIntersectionObserver = (
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) => {
  const [isVisible, setIsVisible] = React.useState(false);

  React.useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsVisible(entry.isIntersecting);
    }, options);

    observer.observe(element);

    return () => observer.disconnect();
  }, [elementRef, options]);

  return isVisible;
};

// Main animated element component
export const AnimatedElement: React.FC<AnimatedElementProps> = ({
  children,
  animation,
  trigger = 'mount',
  isActive = true,
  onAnimationStart,
  onAnimationEnd,
  className,
}) => {
  const [shouldAnimate, setShouldAnimate] = React.useState(trigger === 'mount');
  const [isHovered, setIsHovered] = React.useState(false);
  const [isFocused, setIsFocused] = React.useState(false);
  const [isClicked, setIsClicked] = React.useState(false);
  
  const elementRef = React.useRef<HTMLDivElement>(null);
  const isVisible = useIntersectionObserver(elementRef, { threshold: 0.1 });

  // Handle different triggers
  React.useEffect(() => {
    switch (trigger) {
      case 'mount':
        setShouldAnimate(isActive);
        break;
      case 'visible':
        setShouldAnimate(isVisible && isActive);
        break;
      case 'manual':
        setShouldAnimate(isActive);
        break;
      case 'hover':
        setShouldAnimate(isHovered && isActive);
        break;
      case 'focus':
        setShouldAnimate(isFocused && isActive);
        break;
      case 'click':
        setShouldAnimate(isClicked && isActive);
        break;
    }
  }, [trigger, isActive, isVisible, isHovered, isFocused, isClicked]);

  // Handle click animation reset
  React.useEffect(() => {
    if (trigger === 'click' && isClicked) {
      const timer = setTimeout(() => setIsClicked(false), 300);
      return () => clearTimeout(timer);
    }
  }, [trigger, isClicked]);

  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);
  const handleClick = () => setIsClicked(true);

  const handleAnimationStart = () => {
    onAnimationStart?.();
  };

  const handleAnimationEnd = () => {
    onAnimationEnd?.();
  };

  const getAnimationClasses = () => {
    if (!shouldAnimate) return '';

    const classes = [
      animationClasses[animation.type],
      durationClasses[animation.duration || 'normal'],
      easingClasses[animation.easing || 'ease-out'],
    ];

    return classes.filter(Boolean).join(' ');
  };

  const getAnimationStyle = (): React.CSSProperties => {
    const style: React.CSSProperties = {};

    if (animation.delay) {
      style.animationDelay = `${animation.delay}ms`;
    }

    if (animation.repeat) {
      style.animationIterationCount = animation.repeat;
    }

    if (animation.direction) {
      style.animationDirection = animation.direction;
    }

    if (animation.fillMode) {
      style.animationFillMode = animation.fillMode;
    }

    return style;
  };

  return (
    <div
      ref={elementRef}
      className={cn(getAnimationClasses(), className)}
      style={getAnimationStyle()}
      onMouseEnter={trigger === 'hover' ? handleMouseEnter : undefined}
      onMouseLeave={trigger === 'hover' ? handleMouseLeave : undefined}
      onFocus={trigger === 'focus' ? handleFocus : undefined}
      onBlur={trigger === 'focus' ? handleBlur : undefined}
      onClick={trigger === 'click' ? handleClick : undefined}
      onAnimationStart={handleAnimationStart}
      onAnimationEnd={handleAnimationEnd}
    >
      {children}
    </div>
  );
};

// Stagger animation component for lists
export interface StaggeredAnimationProps {
  children: React.ReactNode[];
  animation: AnimationConfig;
  staggerDelay?: number;
  trigger?: 'mount' | 'visible';
  className?: string;
}

export const StaggeredAnimation: React.FC<StaggeredAnimationProps> = ({
  children,
  animation,
  staggerDelay = 100,
  trigger = 'mount',
  className,
}) => {
  const [isActive, setIsActive] = React.useState(trigger === 'mount');
  const containerRef = React.useRef<HTMLDivElement>(null);
  const isVisible = useIntersectionObserver(containerRef, { threshold: 0.1 });

  React.useEffect(() => {
    if (trigger === 'visible') {
      setIsActive(isVisible);
    }
  }, [trigger, isVisible]);

  return (
    <div ref={containerRef} className={className}>
      {children.map((child, index) => (
        <AnimatedElement
          key={index}
          animation={{
            ...animation,
            delay: (animation.delay || 0) + (index * staggerDelay),
          }}
          trigger="manual"
          isActive={isActive}
        >
          {child}
        </AnimatedElement>
      ))}
    </div>
  );
};

// Preset animation components
export const FadeIn: React.FC<Omit<AnimatedElementProps, 'animation'> & { duration?: AnimationDuration }> = ({
  duration = 'normal',
  ...props
}) => (
  <AnimatedElement animation={{ type: 'fadeIn', duration }} {...props} />
);

export const SlideUp: React.FC<Omit<AnimatedElementProps, 'animation'> & { duration?: AnimationDuration }> = ({
  duration = 'normal',
  ...props
}) => (
  <AnimatedElement animation={{ type: 'slideUp', duration }} {...props} />
);

export const ScaleIn: React.FC<Omit<AnimatedElementProps, 'animation'> & { duration?: AnimationDuration }> = ({
  duration = 'normal',
  ...props
}) => (
  <AnimatedElement animation={{ type: 'scaleIn', duration }} {...props} />
);

export const Bounce: React.FC<Omit<AnimatedElementProps, 'animation'> & { repeat?: number | 'infinite' }> = ({
  repeat = 'infinite',
  ...props
}) => (
  <AnimatedElement animation={{ type: 'bounce', repeat }} {...props} />
);

export const Pulse: React.FC<Omit<AnimatedElementProps, 'animation'> & { repeat?: number | 'infinite' }> = ({
  repeat = 'infinite',
  ...props
}) => (
  <AnimatedElement animation={{ type: 'pulse', repeat }} {...props} />
);

// Animation sequence component
export interface AnimationSequenceProps {
  children: React.ReactNode;
  sequence: AnimationConfig[];
  onSequenceComplete?: () => void;
  className?: string;
}

export const AnimationSequence: React.FC<AnimationSequenceProps> = ({
  children,
  sequence,
  onSequenceComplete,
  className,
}) => {
  const [currentStep, setCurrentStep] = React.useState(0);
  const [isActive, setIsActive] = React.useState(true);

  const handleAnimationEnd = () => {
    if (currentStep < sequence.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setIsActive(false);
      onSequenceComplete?.();
    }
  };

  const currentAnimation = sequence[currentStep];

  if (!currentAnimation || !isActive) {
    return <div className={className}>{children}</div>;
  }

  return (
    <AnimatedElement
      animation={currentAnimation}
      trigger="manual"
      isActive={isActive}
      onAnimationEnd={handleAnimationEnd}
      className={className}
    >
      {children}
    </AnimatedElement>
  );
};

export default {
  AnimatedElement,
  StaggeredAnimation,
  FadeIn,
  SlideUp,
  ScaleIn,
  Bounce,
  Pulse,
  AnimationSequence,
};
