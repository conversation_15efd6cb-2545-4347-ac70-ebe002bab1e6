import * as React from "react"
import { useEffect, useState, useCallback } from "react"
import { useNavigate } from "react-router-dom"
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import {
  Calculator,
  Calendar,
  CreditCard,
  Settings,
  Smile,
  User,
  Zap,
  FileText,
  History,
  Monitor,
  Play,
  Square,
  RotateCcw,
  Download,
  Upload,
  Search,
  Home,
  Database,
  Code,
  Terminal,
  GitBranch,
  Palette,
  Moon,
  Sun,
  HelpCircle,
  ExternalLink,
} from "lucide-react"
import { useTheme } from "@/components/theme-provider"
import { toast } from "@/hooks/use-toast"
import { SafeFragment } from "@/components/ui/SafeFragment"

interface CommandAction {
  id: string
  label: string
  description?: string
  icon: React.ReactNode
  keywords: string[]
  action: () => void
  group: string
  shortcut?: string
}

// Custom hook for safe navigation
function useSafeNavigate() {
  try {
    return useNavigate()
  } catch (error) {
    // Router context not available, using fallback navigation
    return (path: string) => {
      window.location.href = path
    }
  }
}

export function CommandPalette() {
  const [open, setOpen] = useState(false)
  const navigate = useSafeNavigate()
  const { setTheme, theme } = useTheme()

  // Define all available commands
  const commands: CommandAction[] = [
    // Navigation
    {
      id: "nav-dashboard",
      label: "Go to Dashboard",
      description: "Navigate to the main dashboard",
      icon: <Home className="h-4 w-4" />,
      keywords: ["dashboard", "home", "main"],
      action: () => navigate("/dashboard"),
      group: "Navigation",
    },
    {
      id: "nav-monitoring",
      label: "Go to Monitoring",
      description: "View system monitoring dashboard",
      icon: <Monitor className="h-4 w-4" />,
      keywords: ["monitoring", "metrics", "performance"],
      action: () => navigate("/monitoring"),
      group: "Navigation",
    },
    {
      id: "nav-history",
      label: "Go to History",
      description: "View transformation history",
      icon: <History className="h-4 w-4" />,
      keywords: ["history", "past", "transformations"],
      action: () => navigate("/history"),
      group: "Navigation",
    },
    {
      id: "nav-settings",
      label: "Go to Settings",
      description: "Configure application settings",
      icon: <Settings className="h-4 w-4" />,
      keywords: ["settings", "config", "preferences"],
      action: () => navigate("/settings"),
      group: "Navigation",
    },

    // Reactor Actions
    {
      id: "reactor-start",
      label: "Start Reactor",
      description: "Begin code transformation process",
      icon: <Play className="h-4 w-4" />,
      keywords: ["start", "begin", "run", "execute"],
      action: () => {
        toast({
          title: "Reactor Started",
          description: "Code transformation process initiated",
        })
        // TODO: Implement actual reactor start logic
      },
      group: "Reactor",
      shortcut: "⌘ + R",
    },
    {
      id: "reactor-stop",
      label: "Stop Reactor",
      description: "Halt the transformation process",
      icon: <Square className="h-4 w-4" />,
      keywords: ["stop", "halt", "cancel", "abort"],
      action: () => {
        toast({
          title: "Reactor Stopped",
          description: "Transformation process halted",
        })
        // TODO: Implement actual reactor stop logic
      },
      group: "Reactor",
      shortcut: "⌘ + S",
    },
    {
      id: "reactor-reset",
      label: "Reset Reactor",
      description: "Reset reactor to initial state",
      icon: <RotateCcw className="h-4 w-4" />,
      keywords: ["reset", "restart", "clear", "initialize"],
      action: () => {
        toast({
          title: "Reactor Reset",
          description: "System restored to initial state",
        })
        // TODO: Implement actual reactor reset logic
      },
      group: "Reactor",
    },

    // Session Management
    {
      id: "session-new",
      label: "New Session",
      description: "Create a new transformation session",
      icon: <FileText className="h-4 w-4" />,
      keywords: ["new", "create", "session", "fresh"],
      action: () => {
        toast({
          title: "New Session Created",
          description: "Ready for new transformation",
        })
        // TODO: Implement session creation
      },
      group: "Session",
      shortcut: "⌘ + N",
    },
    {
      id: "session-save",
      label: "Save Session",
      description: "Save current session state",
      icon: <Download className="h-4 w-4" />,
      keywords: ["save", "export", "backup"],
      action: () => {
        toast({
          title: "Session Saved",
          description: "Current state has been saved",
        })
        // TODO: Implement session save
      },
      group: "Session",
      shortcut: "⌘ + S",
    },
    {
      id: "session-load",
      label: "Load Session",
      description: "Load a previous session",
      icon: <Upload className="h-4 w-4" />,
      keywords: ["load", "import", "restore", "open"],
      action: () => {
        toast({
          title: "Load Session",
          description: "Select a session to load",
        })
        // TODO: Implement session load dialog
      },
      group: "Session",
      shortcut: "⌘ + O",
    },

    // Theme & Appearance
    {
      id: "theme-toggle",
      label: "Toggle Theme",
      description: `Switch to ${theme === "dark" ? "light" : "dark"} mode`,
      icon: theme === "dark" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />,
      keywords: ["theme", "dark", "light", "mode"],
      action: () => setTheme(theme === "dark" ? "light" : "dark"),
      group: "Appearance",
      shortcut: "⌘ + T",
    },

    // Documentation & Help
    {
      id: "docs-search",
      label: "Search Documentation",
      description: "Search through documentation",
      icon: <Search className="h-4 w-4" />,
      keywords: ["docs", "documentation", "help", "search"],
      action: () => {
        window.open("https://docs.metamorphic-reactor.dev", "_blank")
      },
      group: "Help",
      shortcut: "⌘ + ?",
    },
    {
      id: "help-shortcuts",
      label: "Keyboard Shortcuts",
      description: "View all keyboard shortcuts",
      icon: <HelpCircle className="h-4 w-4" />,
      keywords: ["shortcuts", "hotkeys", "keyboard"],
      action: () => {
        toast({
          title: "Keyboard Shortcuts",
          description: "⌘K - Command Palette, ⌘R - Start Reactor, ⌘S - Save",
        })
      },
      group: "Help",
    },

    // Billing & Account
    {
      id: "billing-usage",
      label: "View Usage & Billing",
      description: "Check current usage and billing information",
      icon: <CreditCard className="h-4 w-4" />,
      keywords: ["billing", "usage", "cost", "payment"],
      action: () => navigate("/billing"),
      group: "Account",
    },
    {
      id: "account-profile",
      label: "Account Profile",
      description: "Manage your account settings",
      icon: <User className="h-4 w-4" />,
      keywords: ["account", "profile", "user"],
      action: () => navigate("/profile"),
      group: "Account",
    },
  ]

  // Keyboard shortcut handler
  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  // Handle command execution
  const runCommand = useCallback((command: CommandAction) => {
    setOpen(false)
    command.action()
  }, [])

  // Group commands by category
  const groupedCommands = commands.reduce((acc, command) => {
    if (!acc[command.group]) {
      acc[command.group] = []
    }
    acc[command.group].push(command)
    return acc
  }, {} as Record<string, CommandAction[]>)

  return (
    <CommandDialog open={open} onOpenChange={setOpen}>
      <CommandInput 
        placeholder="Type a command or search..." 
        className="h-12"
      />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        
        {Object.entries(groupedCommands).map(([group, commands], index) => (
          <SafeFragment key={group}>
            {index > 0 && <CommandSeparator />}
            <CommandGroup heading={group}>
              {commands.map((command) => (
                <CommandItem
                  key={command.id}
                  value={`${command.label} ${command.keywords.join(" ")}`}
                  onSelect={() => runCommand(command)}
                  className="flex items-center gap-3 px-3 py-3 cursor-pointer"
                >
                  {command.icon}
                  <div className="flex-1">
                    <div className="font-medium">{command.label}</div>
                    {command.description && (
                      <div className="text-sm text-muted-foreground">
                        {command.description}
                      </div>
                    )}
                  </div>
                  {command.shortcut && (
                    <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                      {command.shortcut}
                    </div>
                  )}
                </CommandItem>
              ))}
            </CommandGroup>
          </SafeFragment>
        ))}
      </CommandList>
    </CommandDialog>
  )
}

// Hook to trigger command palette programmatically
export function useCommandPalette() {
  const [open, setOpen] = useState(false)
  
  const openCommandPalette = useCallback(() => {
    setOpen(true)
  }, [])
  
  const closeCommandPalette = useCallback(() => {
    setOpen(false)
  }, [])
  
  return {
    open,
    openCommandPalette,
    closeCommandPalette,
  }
}
