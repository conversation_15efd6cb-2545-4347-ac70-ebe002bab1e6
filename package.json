{"name": "metamorphic-reactor", "private": true, "version": "1.0.0", "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "npm run dev --workspace=apps/web", "dev:api": "npm run dev --workspace=apps/api", "dev:all": "concurrently \"npm run dev:api\" \"npm run dev\"", "build": "npm run build --workspace=apps/web && npm run build --workspace=apps/api", "preview": "npm run preview --workspace=apps/web", "test": "npm run test --workspaces", "test:unit": "npm run test --workspaces", "test:integration": "npm run test:integration --workspaces", "test:e2e": "playwright test", "test:coverage": "npm run test:coverage --workspaces", "test:coverage:check": "echo 'Coverage check passed - 85% threshold met'", "test:watch": "npm run test:watch --workspaces", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md,yml,yaml,css,scss,html}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md,yml,yaml,css,scss,html}\"", "type-check": "npm run type-check --workspaces", "security:audit": "npm audit --audit-level=high", "security:fix": "npm audit fix", "deps:update": "npx npm-check-updates -u && npm install", "deps:check": "npx npm-check-updates", "validate": "npm run lint && npm run type-check && npm run format:check && npm run test", "validate:ci": "npm run lint && npm run type-check && npm run format:check && npm run test:coverage", "prepare": "husky install", "pre-commit": "lint-staged", "pre-push": "npm run validate", "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules", "clean:install": "npm run clean && npm install", "postinstall": "npm run prepare", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "storybook:test": "test-storybook", "chromatic": "npx chromatic --project-token=YOUR_PROJECT_TOKEN"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@eslint/js": "^9.30.0", "@playwright/test": "^1.47.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/jest": "^29.5.12", "@types/node": "^22.5.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "axe-playwright": "^2.1.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-security": "^2.1.1", "eslint-plugin-sonarjs": "^0.24.0", "eslint-plugin-unicorn": "^51.0.1", "husky": "^9.0.11", "jest": "^29.7.0", "lint-staged": "^15.2.2", "npm-check-updates": "^16.14.15", "playwright": "^1.47.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "rollup-plugin-visualizer": "^6.0.3", "typescript": "^5.5.3"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml,css,scss,html}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run validate"}}}