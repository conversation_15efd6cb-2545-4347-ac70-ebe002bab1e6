import * as React from "react"
import { cn } from "@/lib/utils"
import { Loader2 } from "lucide-react"
import { Progress } from "./progress"

// Enhanced skeleton component
interface SkeletonProps {
  className?: string
  width?: string | number
  height?: string | number
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded'
  animation?: 'pulse' | 'wave' | 'none'
}

export function Skeleton({
  className,
  width,
  height,
  variant = 'rectangular',
  animation = 'pulse'
}: SkeletonProps) {
  const variantClasses = {
    text: 'h-4 rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-none',
    rounded: 'rounded-md'
  }

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-shimmer',
    none: ''
  }

  return (
    <div
      className={cn(
        "bg-muted",
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height
      }}
    />
  )
}

// Text skeleton with multiple lines
interface TextSkeletonProps {
  lines?: number
  className?: string
  lastLineWidth?: string
}

export function TextSkeleton({ 
  lines = 3, 
  className,
  lastLineWidth = "60%" 
}: TextSkeletonProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          width={index === lines - 1 ? lastLineWidth : "100%"}
        />
      ))}
    </div>
  )
}

// Card skeleton
interface CardSkeletonProps {
  showAvatar?: boolean
  showImage?: boolean
  lines?: number
  className?: string
}

export function CardSkeleton({ 
  showAvatar = false, 
  showImage = false, 
  lines = 3,
  className 
}: CardSkeletonProps) {
  return (
    <div className={cn("p-4 border border-border rounded-lg", className)}>
      <div className="space-y-4">
        {/* Header with avatar */}
        {showAvatar && (
          <div className="flex items-center space-x-3">
            <Skeleton variant="circular" width={40} height={40} />
            <div className="space-y-2 flex-1">
              <Skeleton variant="text" width="40%" />
              <Skeleton variant="text" width="60%" />
            </div>
          </div>
        )}

        {/* Image */}
        {showImage && (
          <Skeleton variant="rounded" className="w-full h-48" />
        )}

        {/* Content */}
        <TextSkeleton lines={lines} />
      </div>
    </div>
  )
}

// Loading spinner with message
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  message?: string
  progress?: number
  className?: string
}

export function LoadingSpinner({ 
  size = 'md', 
  message,
  progress,
  className 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  return (
    <div className={cn("flex flex-col items-center justify-center space-y-3 py-8", className)}>
      <Loader2 className={cn("animate-spin text-primary", sizeClasses[size])} />
      {message && (
        <p className="text-sm text-muted-foreground text-center">
          {message}
        </p>
      )}
      {progress !== undefined && (
        <div className="w-48 space-y-2">
          <Progress value={progress} className="h-1" />
          <p className="text-xs text-muted-foreground text-center">
            {Math.round(progress)}% complete
          </p>
        </div>
      )}
    </div>
  )
}

// Loading overlay
interface LoadingOverlayProps {
  isLoading: boolean
  children: React.ReactNode
  message?: string
  className?: string
}

export function LoadingOverlay({ 
  isLoading, 
  children, 
  message = "Loading...",
  className 
}: LoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <LoadingSpinner message={message} />
        </div>
      )}
    </div>
  )
}

// Button loading state
interface ButtonLoadingProps {
  isLoading: boolean
  children: React.ReactNode
  loadingText?: string
}

export function ButtonLoading({
  isLoading,
  children,
  loadingText
}: ButtonLoadingProps) {
  if (isLoading) {
    return (
      <>
        <Loader2 className="w-4 h-4 animate-spin mr-2" />
        {loadingText || 'Loading...'}
      </>
    )
  }
  return <>{children}</>
}

// Code editor skeleton
interface CodeEditorSkeletonProps {
  height?: string | number
  className?: string
}

export function CodeEditorSkeleton({
  height = 400,
  className
}: CodeEditorSkeletonProps) {
  const heightStyle = typeof height === 'number' ? `${height}px` : height

  return (
    <div
      className={cn(
        "bg-background border border-border rounded-lg overflow-hidden",
        className
      )}
      style={{ height: heightStyle }}
    >
      {/* Editor toolbar */}
      <div className="flex items-center justify-between p-2 border-b border-border bg-muted/50">
        <div className="flex items-center space-x-2">
          <Skeleton width={60} height={20} />
          <Skeleton width={80} height={20} />
        </div>
        <div className="flex items-center space-x-1">
          <Skeleton width={24} height={24} variant="circular" />
          <Skeleton width={24} height={24} variant="circular" />
        </div>
      </div>

      {/* Editor content skeleton */}
      <div className="flex-1 p-4 space-y-3 overflow-hidden">
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-2">
            <div className="w-6 h-4 bg-muted-foreground/10 rounded text-xs flex items-center justify-center text-muted-foreground">
              {i + 1}
            </div>
            <Skeleton
              variant="text"
              height={16}
              width={`${Math.random() * 60 + 20}%`}
              animation="pulse"
              className="animate-pulse"
              style={{ animationDelay: `${i * 100}ms` }}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

// Dashboard widget skeleton
export function DashboardWidgetSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("p-6 border border-border rounded-lg bg-card", className)}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Skeleton width={20} height={20} variant="circular" />
            <Skeleton width={120} height={20} />
          </div>
          <Skeleton width={24} height={24} variant="circular" />
        </div>
        
        {/* Content */}
        <div className="space-y-3">
          <Skeleton width="100%" height={40} />
          <div className="grid grid-cols-2 gap-4">
            <Skeleton width="100%" height={60} />
            <Skeleton width="100%" height={60} />
          </div>
        </div>
      </div>
    </div>
  )
}

export {
  type SkeletonProps,
  type LoadingSpinnerProps,
  type LoadingOverlayProps,
  type ButtonLoadingProps,
  type CodeEditorSkeletonProps
}
