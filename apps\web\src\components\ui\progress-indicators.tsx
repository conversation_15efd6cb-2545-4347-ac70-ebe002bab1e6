import React from 'react';
import { cn } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';
import { Loader2, CheckCircle, XCircle, AlertTriangle, Clock } from 'lucide-react';

// Types
export interface ProgressStep {
  id: string;
  label: string;
  description?: string;
  status: 'pending' | 'active' | 'completed' | 'error' | 'skipped';
  progress?: number;
  duration?: number;
  startTime?: Date;
  endTime?: Date;
}

export interface ProgressIndicatorProps {
  steps: ProgressStep[];
  currentStep?: string;
  orientation?: 'horizontal' | 'vertical';
  showProgress?: boolean;
  showDuration?: boolean;
  animated?: boolean;
  compact?: boolean;
  className?: string;
}

// Step status icons
const getStatusIcon = (status: ProgressStep['status'], animated = true) => {
  const iconClass = cn("w-4 h-4", animated && "transition-all duration-300");
  
  switch (status) {
    case 'pending':
      return <Clock className={cn(iconClass, "text-muted-foreground")} />;
    case 'active':
      return <Loader2 className={cn(iconClass, "text-blue-600 animate-spin")} />;
    case 'completed':
      return <CheckCircle className={cn(iconClass, "text-green-600")} />;
    case 'error':
      return <XCircle className={cn(iconClass, "text-red-600")} />;
    case 'skipped':
      return <AlertTriangle className={cn(iconClass, "text-yellow-600")} />;
    default:
      return <Clock className={cn(iconClass, "text-muted-foreground")} />;
  }
};

// Step status colors
const getStatusColor = (status: ProgressStep['status']) => {
  switch (status) {
    case 'pending': return 'bg-muted text-muted-foreground';
    case 'active': return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'completed': return 'bg-green-100 text-green-800 border-green-200';
    case 'error': return 'bg-red-100 text-red-800 border-red-200';
    case 'skipped': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default: return 'bg-muted text-muted-foreground';
  }
};

// Format duration
const formatDuration = (ms: number) => {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}m`;
};

// Calculate step duration
const getStepDuration = (step: ProgressStep) => {
  if (step.startTime && step.endTime) {
    return step.endTime.getTime() - step.startTime.getTime();
  }
  if (step.startTime && step.status === 'active') {
    return Date.now() - step.startTime.getTime();
  }
  return step.duration || 0;
};

// Progress Indicator Component
export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  steps,
  currentStep,
  orientation = 'horizontal',
  showProgress = true,
  showDuration = false,
  animated = true,
  compact = false,
  className,
}) => {
  const currentStepIndex = currentStep ? steps.findIndex(step => step.id === currentStep) : -1;
  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const totalProgress = (completedSteps / steps.length) * 100;

  if (orientation === 'vertical') {
    return (
      <div className={cn("space-y-4", className)}>
        {showProgress && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">{completedSteps}/{steps.length}</span>
            </div>
            <Progress value={totalProgress} className="h-2" />
          </div>
        )}
        
        <div className="space-y-3">
          {steps.map((step, index) => {
            const duration = getStepDuration(step);
            const isActive = step.id === currentStep || step.status === 'active';
            
            return (
              <div
                key={step.id}
                className={cn(
                  "flex items-start space-x-3 p-3 rounded-lg border transition-all duration-300",
                  getStatusColor(step.status),
                  isActive && "ring-2 ring-blue-500/20",
                  animated && "hover:shadow-sm"
                )}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {getStatusIcon(step.status, animated)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className={cn(
                      "text-sm font-medium truncate",
                      isActive && "font-semibold"
                    )}>
                      {step.label}
                    </h4>
                    
                    {showDuration && duration > 0 && (
                      <span className="text-xs text-muted-foreground ml-2">
                        {formatDuration(duration)}
                      </span>
                    )}
                  </div>
                  
                  {step.description && !compact && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {step.description}
                    </p>
                  )}
                  
                  {step.progress !== undefined && step.status === 'active' && (
                    <div className="mt-2">
                      <Progress value={step.progress} className="h-1" />
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // Horizontal layout
  return (
    <div className={cn("w-full", className)}>
      {showProgress && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Progress</span>
            <span className="text-sm text-muted-foreground">{completedSteps}/{steps.length}</span>
          </div>
          <Progress value={totalProgress} className="h-2" />
        </div>
      )}
      
      <div className="flex items-center space-x-4 overflow-x-auto pb-2">
        {steps.map((step, index) => {
          const duration = getStepDuration(step);
          const isActive = step.id === currentStep || step.status === 'active';
          const isLast = index === steps.length - 1;
          
          return (
            <React.Fragment key={step.id}>
              <div className={cn(
                "flex flex-col items-center space-y-2 min-w-0 flex-shrink-0",
                compact ? "w-16" : "w-24"
              )}>
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300",
                  step.status === 'completed' && "bg-green-600 border-green-600 text-white",
                  step.status === 'active' && "bg-blue-600 border-blue-600 text-white",
                  step.status === 'error' && "bg-red-600 border-red-600 text-white",
                  step.status === 'pending' && "bg-muted border-muted-foreground/30",
                  isActive && "ring-2 ring-blue-500/20 scale-110"
                )}>
                  {getStatusIcon(step.status, animated)}
                </div>
                
                <div className="text-center">
                  <div className={cn(
                    "text-xs font-medium truncate",
                    isActive && "font-semibold text-blue-600"
                  )}>
                    {step.label}
                  </div>
                  
                  {showDuration && duration > 0 && (
                    <div className="text-xs text-muted-foreground">
                      {formatDuration(duration)}
                    </div>
                  )}
                </div>
                
                {step.progress !== undefined && step.status === 'active' && !compact && (
                  <div className="w-full">
                    <Progress value={step.progress} className="h-1" />
                  </div>
                )}
              </div>
              
              {!isLast && (
                <div className={cn(
                  "flex-1 h-0.5 transition-all duration-300",
                  index < currentStepIndex || step.status === 'completed' 
                    ? "bg-green-600" 
                    : "bg-muted"
                )} />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

// Circular Progress Component
export interface CircularProgressProps {
  value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  showValue?: boolean;
  showPercentage?: boolean;
  color?: string;
  backgroundColor?: string;
  animated?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  value,
  max = 100,
  size = 120,
  strokeWidth = 8,
  showValue = true,
  showPercentage = true,
  color = "currentColor",
  backgroundColor = "#e5e7eb",
  animated = true,
  className,
  children,
}) => {
  const percentage = Math.min((value / max) * 100, 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div 
      className={cn("relative inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        width={size}
        height={size}
        className={cn("transform -rotate-90", animated && "transition-all duration-500")}
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="none"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn(animated && "transition-all duration-500 ease-out")}
        />
      </svg>
      
      {/* Center content */}
      <div className="absolute inset-0 flex items-center justify-center">
        {children || (
          <div className="text-center">
            {showValue && (
              <div className="text-lg font-semibold">
                {Math.round(value)}{showPercentage && '%'}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Pulse Loader Component
export interface PulseLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  count?: number;
  className?: string;
}

export const PulseLoader: React.FC<PulseLoaderProps> = ({
  size = 'md',
  color = 'currentColor',
  count = 3,
  className,
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className={cn(
            "rounded-full animate-pulse",
            sizeClasses[size]
          )}
          style={{
            backgroundColor: color,
            animationDelay: `${index * 0.2}s`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  );
};

export default ProgressIndicator;
