{"ci": {"collect": {"url": ["http://localhost:4173/", "http://localhost:4173/dashboard"], "startServerCommand": "cd apps/web && npm run preview", "startServerReadyPattern": "Local:", "startServerReadyTimeout": 60000, "numberOfRuns": 5, "settings": {"preset": "mobile", "chromeFlags": "--no-sandbox --disable-dev-shm-usage", "emulatedFormFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "cpuSlowdownMultiplier": 4, "requestLatencyMs": 150, "downloadThroughputKbps": 1638.4, "uploadThroughputKbps": 750}, "screenEmulation": {"mobile": true, "width": 375, "height": 667, "deviceScaleFactor": 2, "disabled": false}, "onlyAudits": ["largest-contentful-paint", "cumulative-layout-shift", "max-potential-fid", "first-contentful-paint", "speed-index", "interactive", "total-blocking-time"]}}, "assert": {"assertions": {"audits:largest-contentful-paint": ["error", {"maxNumericValue": 3000}], "audits:cumulative-layout-shift": ["error", {"maxNumericValue": 0.05}], "audits:max-potential-fid": ["error", {"maxNumericValue": 200}], "audits:first-contentful-paint": ["warn", {"maxNumericValue": 2000}], "audits:speed-index": ["warn", {"maxNumericValue": 3500}], "audits:interactive": ["warn", {"maxNumericValue": 6000}], "audits:total-blocking-time": ["warn", {"maxNumericValue": 300}]}}, "upload": {"target": "temporary-public-storage", "outputDir": "./lhci_reports/cwv"}}}