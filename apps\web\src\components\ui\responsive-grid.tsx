import React from 'react';
import { cn } from '@/lib/utils';
import { useResponsive, Breakpoint } from '@/hooks/useResponsive';

// Grid configuration types
export type GridCols = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
export type GridSpan = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'full' | 'auto';
export type GridGap = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;

// Responsive grid configuration
export interface ResponsiveGridConfig {
  cols?: Partial<Record<Breakpoint, GridCols>>;
  gap?: Partial<Record<Breakpoint, GridGap>>;
}

export interface ResponsiveGridItemConfig {
  span?: Partial<Record<Breakpoint, GridSpan>>;
  start?: Partial<Record<Breakpoint, number>>;
  end?: Partial<Record<Breakpoint, number>>;
}

// Grid container component
export interface ResponsiveGridProps extends ResponsiveGridConfig {
  children: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  cols = { xs: 1, sm: 2, md: 3, lg: 4 },
  gap = { xs: 4, sm: 4, md: 6, lg: 6 },
  className,
  as: Component = 'div',
}) => {
  const { breakpoint } = useResponsive();

  // Get current values based on breakpoint
  const getCurrentValue = <T,>(values: Partial<Record<Breakpoint, T>>, fallback: T): T => {
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = breakpointOrder.indexOf(breakpoint);
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i];
      if (values[bp] !== undefined) {
        return values[bp]!;
      }
    }
    
    return fallback;
  };

  const currentCols = getCurrentValue(cols, 1);
  const currentGap = getCurrentValue(gap, 4);

  const gridClasses = cn(
    'grid',
    `grid-cols-${currentCols}`,
    `gap-${currentGap}`,
    className
  );

  return (
    <Component className={gridClasses}>
      {children}
    </Component>
  );
};

// Grid item component
export interface ResponsiveGridItemProps extends ResponsiveGridItemConfig {
  children: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

export const ResponsiveGridItem: React.FC<ResponsiveGridItemProps> = ({
  children,
  span = { xs: 1 },
  start,
  end,
  className,
  as: Component = 'div',
}) => {
  const { breakpoint } = useResponsive();

  const getCurrentValue = <T,>(values: Partial<Record<Breakpoint, T>>, fallback: T): T => {
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = breakpointOrder.indexOf(breakpoint);
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i];
      if (values[bp] !== undefined) {
        return values[bp]!;
      }
    }
    
    return fallback;
  };

  const currentSpan = getCurrentValue(span, 1);
  const currentStart = start ? getCurrentValue(start, undefined) : undefined;
  const currentEnd = end ? getCurrentValue(end, undefined) : undefined;

  const itemClasses = cn(
    currentSpan === 'full' ? 'col-span-full' :
    currentSpan === 'auto' ? 'col-auto' :
    `col-span-${currentSpan}`,
    currentStart && `col-start-${currentStart}`,
    currentEnd && `col-end-${currentEnd}`,
    className
  );

  return (
    <Component className={itemClasses}>
      {children}
    </Component>
  );
};

// Responsive flex container
export interface ResponsiveFlexProps {
  children: React.ReactNode;
  direction?: Partial<Record<Breakpoint, 'row' | 'col' | 'row-reverse' | 'col-reverse'>>;
  wrap?: Partial<Record<Breakpoint, 'wrap' | 'nowrap' | 'wrap-reverse'>>;
  justify?: Partial<Record<Breakpoint, 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly'>>;
  align?: Partial<Record<Breakpoint, 'start' | 'end' | 'center' | 'baseline' | 'stretch'>>;
  gap?: Partial<Record<Breakpoint, GridGap>>;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

export const ResponsiveFlex: React.FC<ResponsiveFlexProps> = ({
  children,
  direction = { xs: 'col', md: 'row' },
  wrap = { xs: 'wrap' },
  justify = { xs: 'start' },
  align = { xs: 'start' },
  gap = { xs: 4 },
  className,
  as: Component = 'div',
}) => {
  const { breakpoint } = useResponsive();

  const getCurrentValue = <T,>(values: Partial<Record<Breakpoint, T>>, fallback: T): T => {
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = breakpointOrder.indexOf(breakpoint);
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i];
      if (values[bp] !== undefined) {
        return values[bp]!;
      }
    }
    
    return fallback;
  };

  const currentDirection = getCurrentValue(direction, 'row');
  const currentWrap = getCurrentValue(wrap, 'wrap');
  const currentJustify = getCurrentValue(justify, 'start');
  const currentAlign = getCurrentValue(align, 'start');
  const currentGap = getCurrentValue(gap, 4);

  const flexClasses = cn(
    'flex',
    `flex-${currentDirection}`,
    `flex-${currentWrap}`,
    `justify-${currentJustify}`,
    `items-${currentAlign}`,
    `gap-${currentGap}`,
    className
  );

  return (
    <Component className={flexClasses}>
      {children}
    </Component>
  );
};

// Responsive container with max-width constraints
export interface ResponsiveContainerProps {
  children: React.ReactNode;
  maxWidth?: Partial<Record<Breakpoint, 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none'>>;
  padding?: Partial<Record<Breakpoint, GridGap>>;
  margin?: Partial<Record<Breakpoint, 'auto' | GridGap>>;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  maxWidth = { xs: 'full', sm: 'sm', md: 'md', lg: 'lg', xl: 'xl', '2xl': '2xl' },
  padding = { xs: 4, sm: 6, lg: 8 },
  margin = { xs: 'auto' },
  className,
  as: Component = 'div',
}) => {
  const { breakpoint } = useResponsive();

  const getCurrentValue = <T,>(values: Partial<Record<Breakpoint, T>>, fallback: T): T => {
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = breakpointOrder.indexOf(breakpoint);
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i];
      if (values[bp] !== undefined) {
        return values[bp]!;
      }
    }
    
    return fallback;
  };

  const currentMaxWidth = getCurrentValue(maxWidth, 'full');
  const currentPadding = getCurrentValue(padding, 4);
  const currentMargin = getCurrentValue(margin, 'auto');

  const containerClasses = cn(
    'w-full',
    currentMaxWidth !== 'none' && currentMaxWidth !== 'full' && `max-w-${currentMaxWidth}`,
    currentMaxWidth === 'full' && 'max-w-full',
    `px-${currentPadding}`,
    currentMargin === 'auto' && 'mx-auto',
    typeof currentMargin === 'number' && `mx-${currentMargin}`,
    className
  );

  return (
    <Component className={containerClasses}>
      {children}
    </Component>
  );
};

// Responsive spacing component
export interface ResponsiveSpacingProps {
  size?: Partial<Record<Breakpoint, GridGap>>;
  direction?: 'x' | 'y' | 'top' | 'right' | 'bottom' | 'left' | 'all';
  type?: 'margin' | 'padding';
  className?: string;
}

export const ResponsiveSpacing: React.FC<ResponsiveSpacingProps> = ({
  size = { xs: 4 },
  direction = 'all',
  type = 'margin',
  className,
}) => {
  const { breakpoint } = useResponsive();

  const getCurrentValue = <T,>(values: Partial<Record<Breakpoint, T>>, fallback: T): T => {
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = breakpointOrder.indexOf(breakpoint);
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i];
      if (values[bp] !== undefined) {
        return values[bp]!;
      }
    }
    
    return fallback;
  };

  const currentSize = getCurrentValue(size, 4);
  const prefix = type === 'margin' ? 'm' : 'p';

  const getSpacingClass = () => {
    switch (direction) {
      case 'x': return `${prefix}x-${currentSize}`;
      case 'y': return `${prefix}y-${currentSize}`;
      case 'top': return `${prefix}t-${currentSize}`;
      case 'right': return `${prefix}r-${currentSize}`;
      case 'bottom': return `${prefix}b-${currentSize}`;
      case 'left': return `${prefix}l-${currentSize}`;
      case 'all': return `${prefix}-${currentSize}`;
      default: return `${prefix}-${currentSize}`;
    }
  };

  return <div className={cn(getSpacingClass(), className)} />;
};

// Responsive visibility component
export interface ResponsiveVisibilityProps {
  children: React.ReactNode;
  show?: Partial<Record<Breakpoint, boolean>>;
  hide?: Partial<Record<Breakpoint, boolean>>;
  className?: string;
}

export const ResponsiveVisibility: React.FC<ResponsiveVisibilityProps> = ({
  children,
  show,
  hide,
  className,
}) => {
  const { breakpoint } = useResponsive();

  const getCurrentValue = <T,>(values: Partial<Record<Breakpoint, T>>, fallback: T): T => {
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = breakpointOrder.indexOf(breakpoint);
    
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i];
      if (values[bp] !== undefined) {
        return values[bp]!;
      }
    }
    
    return fallback;
  };

  const shouldShow = show ? getCurrentValue(show, true) : true;
  const shouldHide = hide ? getCurrentValue(hide, false) : false;
  const isVisible = shouldShow && !shouldHide;

  if (!isVisible) return null;

  return <div className={className}>{children}</div>;
};

export default {
  ResponsiveGrid,
  ResponsiveGridItem,
  ResponsiveFlex,
  ResponsiveContainer,
  ResponsiveSpacing,
  ResponsiveVisibility,
};
