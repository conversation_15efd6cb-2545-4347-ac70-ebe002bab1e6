name: Quality Gates

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  accessibility-audit:
    name: Accessibility Audit
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build --workspace=apps/web
    
    - name: Start application
      run: |
        npm run preview --workspace=apps/web &
        sleep 10
    
    - name: Run accessibility audit
      run: |
        npx axe-core-cli http://localhost:4173 \
          --exit-on-violations \
          --threshold 97 \
          --reporter json \
          --output-file accessibility-report.json
    
    - name: Upload accessibility report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: accessibility-report
        path: accessibility-report.json

  bundle-budget:
    name: Bundle Budget Check
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build and analyze bundle
      run: |
        npm run build --workspace=apps/web
        npx vite-bundle-analyzer dist --json > bundle-analysis.json
    
    - name: Check bundle size
      run: |
        cd apps/web

        # Calculate actual gzipped bundle size
        BUNDLE_SIZE=$(find dist/assets -name "*.js" -exec gzip -c {} \; | wc -c)
        BUNDLE_SIZE_KB=$((BUNDLE_SIZE / 1024))
        BUDGET_KB=900

        echo "Bundle size: ${BUNDLE_SIZE_KB}KB gzipped (Budget: ${BUDGET_KB}KB)"

        # Check main bundle specifically (react-core chunk)
        MAIN_BUNDLE=$(find dist/assets -name "*react-core*.js" -exec gzip -c {} \; | wc -c)
        MAIN_BUNDLE_KB=$((MAIN_BUNDLE / 1024))
        MAIN_BUDGET_KB=900

        echo "Main bundle: ${MAIN_BUNDLE_KB}KB gzipped (Budget: ${MAIN_BUDGET_KB}KB)"

        # List all chunks for analysis
        echo "Bundle breakdown:"
        find dist/assets -name "*.js" -exec sh -c 'echo "$(basename "$1"): $(gzip -c "$1" | wc -c | awk "{print \$1/1024}")KB"' _ {} \; | sort -k2 -nr

        if [ "$MAIN_BUNDLE_KB" -gt "$MAIN_BUDGET_KB" ]; then
          echo "❌ Main bundle ${MAIN_BUNDLE_KB}KB exceeds budget of ${MAIN_BUDGET_KB}KB"
          exit 1
        else
          echo "✅ Main bundle ${MAIN_BUNDLE_KB}KB is within budget"
        fi
    
    - name: Upload bundle analysis
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: bundle-analysis
        path: bundle-analysis.json

  test-coverage:
    name: Test Coverage
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests with coverage
      run: |
        npm run test:coverage --workspace=apps/api
        npm run test:coverage --workspace=packages/agents
        npm run test:coverage --workspace=apps/web
    
    - name: Check coverage threshold
      run: |
        node -e "
          const fs = require('fs');
          const coverageFiles = [
            'apps/api/coverage/coverage-summary.json',
            'packages/agents/coverage/coverage-summary.json',
            'apps/web/coverage/coverage-summary.json'
          ];
          
          let totalCoverage = 0;
          let validFiles = 0;
          
          coverageFiles.forEach(file => {
            if (fs.existsSync(file)) {
              const coverage = JSON.parse(fs.readFileSync(file, 'utf8'));
              const lineCoverage = coverage.total.lines.pct;
              console.log(\`\${file}: \${lineCoverage}% line coverage\`);
              totalCoverage += lineCoverage;
              validFiles++;
            }
          });
          
          const avgCoverage = validFiles > 0 ? totalCoverage / validFiles : 0;
          const threshold = 90;
          
          console.log(\`Average coverage: \${avgCoverage.toFixed(2)}%\`);
          
          if (avgCoverage < threshold) {
            console.error(\`❌ Coverage \${avgCoverage.toFixed(2)}% below threshold of \${threshold}%\`);
            process.exit(1);
          } else {
            console.log(\`✅ Coverage \${avgCoverage.toFixed(2)}% meets threshold\`);
          }
        "
    
    - name: Upload coverage reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: coverage-reports
        path: |
          apps/api/coverage/
          packages/agents/coverage/
          apps/web/coverage/

  lint-and-format:
    name: Lint and Format Check
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run ESLint
      run: npm run lint --workspace=apps/web
    
    - name: Check Prettier formatting
      run: npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"

  core-web-vitals:
    name: Core Web Vitals Check
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build --workspace=apps/web

    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli@0.12.x

    - name: Start application
      run: |
        npm run preview --workspace=apps/web &
        npx wait-on http://localhost:4173 --timeout 60000

    - name: Run Core Web Vitals audit
      run: |
        # Run Lighthouse with specific focus on Core Web Vitals
        lhci autorun --config=.lighthouserc.cwv.json

    - name: Validate Core Web Vitals thresholds
      run: |
        # Check specific thresholds for our requirements
        node -e "
          const fs = require('fs');
          const reports = JSON.parse(fs.readFileSync('lhci_reports/cwv/manifest.json', 'utf8'));
          const report = reports.find(r => r.isRepresentativeRun);

          const lcp = report.audits['largest-contentful-paint'].numericValue;
          const cls = report.audits['cumulative-layout-shift'].numericValue;
          const inp = report.audits['max-potential-fid'].numericValue;

          console.log(\`Core Web Vitals:\`);
          console.log(\`- LCP: \${lcp}ms (target: ≤3000ms)\`);
          console.log(\`- CLS: \${cls} (target: ≤0.05)\`);
          console.log(\`- INP: \${inp}ms (target: ≤200ms)\`);

          let failed = false;

          if (lcp > 3000) {
            console.error(\`❌ LCP \${lcp}ms exceeds 3000ms threshold\`);
            failed = true;
          }

          if (cls > 0.05) {
            console.error(\`❌ CLS \${cls} exceeds 0.05 threshold\`);
            failed = true;
          }

          if (inp > 200) {
            console.error(\`❌ INP \${inp}ms exceeds 200ms threshold\`);
            failed = true;
          }

          if (failed) {
            process.exit(1);
          } else {
            console.log(\`✅ All Core Web Vitals meet thresholds\`);
          }
        "

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run security audit
      run: npm audit --audit-level moderate

    - name: Run dependency vulnerability check
      run: npx audit-ci --moderate

  quality-gate-summary:
    name: Quality Gate Summary
    runs-on: ubuntu-latest
    needs: [accessibility-audit, bundle-budget, test-coverage, core-web-vitals, lint-and-format, security-audit]
    if: always()

    steps:
    - name: Check all gates passed
      run: |
        echo "🎯 Quality Gate Results Summary:"
        echo "================================"
        echo "- Accessibility (≥97%): ${{ needs.accessibility-audit.result }}"
        echo "- Bundle Budget (≤900KB): ${{ needs.bundle-budget.result }}"
        echo "- Test Coverage (≥90%): ${{ needs.test-coverage.result }}"
        echo "- Core Web Vitals: ${{ needs.core-web-vitals.result }}"
        echo "- Lint & Format: ${{ needs.lint-and-format.result }}"
        echo "- Security Audit: ${{ needs.security-audit.result }}"
        echo "================================"

        if [[ "${{ needs.accessibility-audit.result }}" != "success" ]] || \
           [[ "${{ needs.bundle-budget.result }}" != "success" ]] || \
           [[ "${{ needs.test-coverage.result }}" != "success" ]] || \
           [[ "${{ needs.core-web-vitals.result }}" != "success" ]] || \
           [[ "${{ needs.lint-and-format.result }}" != "success" ]] || \
           [[ "${{ needs.security-audit.result }}" != "success" ]]; then
          echo "❌ One or more quality gates failed"
          echo "Please check the individual job logs for details"
          exit 1
        else
          echo "✅ All quality gates passed! Ready for deployment 🚀"
        fi
