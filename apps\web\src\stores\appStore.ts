import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  github_username?: string;
}

export interface EditorSettings {
  fontSize: number;
  tabSize: number;
  wordWrap: boolean;
  minimap: boolean;
  theme: 'vs-dark' | 'vs-light' | 'hc-black';
  autoSave: boolean;
  formatOnSave: boolean;
}

export interface UIState {
  sidebarOpen: boolean;
  activePanel: 'stream' | 'logs' | 'monitoring';
  showExamples: boolean;
  showHelp: boolean;
  showOnboarding: boolean;
  mobileMenuOpen: boolean;
  mobilePanelOpen: boolean;
  commandPaletteOpen: boolean;
  currentTransformationId: string | null;
}

export interface TransformationState {
  isRunning: boolean;
  currentPrompt: string;
  transformedCode: string;
  diffContent: string;
  iterations: number;
  maxIterations: number;
  currentScore: number;
  targetScore: number;
  cost: number;
  startTime: Date | null;
  endTime: Date | null;
}

export interface NotificationState {
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    timestamp: Date;
    read: boolean;
    persistent?: boolean;
  }>;
  unreadCount: number;
}

export interface AppState {
  // User state
  user: User | null;
  isAuthenticated: boolean;
  
  // UI state
  ui: UIState;
  
  // Editor settings
  editorSettings: EditorSettings;
  
  // Transformation state
  transformation: TransformationState;
  
  // Notifications
  notifications: NotificationState;
  
  // GitHub integration
  github: {
    connected: boolean;
    user: any | null;
    repositories: any[];
    selectedRepo: string | null;
  };
  
  // System preferences
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    autoSave: boolean;
    telemetryEnabled: boolean;
  };
}

export interface AppActions {
  // User actions
  setUser: (user: User | null) => void;
  setAuthenticated: (authenticated: boolean) => void;
  logout: () => void;
  
  // UI actions
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setActivePanel: (panel: UIState['activePanel']) => void;
  toggleExamples: () => void;
  toggleHelp: () => void;
  toggleOnboarding: () => void;
  setMobileMenuOpen: (open: boolean) => void;
  setMobilePanelOpen: (open: boolean) => void;
  toggleCommandPalette: () => void;
  setCurrentTransformationId: (id: string | null) => void;
  
  // Editor actions
  updateEditorSettings: (settings: Partial<EditorSettings>) => void;
  resetEditorSettings: () => void;
  
  // Transformation actions
  startTransformation: (prompt: string, options?: Partial<TransformationState>) => void;
  stopTransformation: () => void;
  updateTransformationProgress: (updates: Partial<TransformationState>) => void;
  resetTransformation: () => void;
  
  // Notification actions
  addNotification: (notification: Omit<NotificationState['notifications'][0], 'id' | 'timestamp'>) => void;
  markNotificationRead: (id: string) => void;
  markAllNotificationsRead: () => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // GitHub actions
  setGitHubConnected: (connected: boolean) => void;
  setGitHubUser: (user: any) => void;
  setGitHubRepositories: (repos: any[]) => void;
  setSelectedRepository: (repo: string | null) => void;
  
  // Preference actions
  setTheme: (theme: AppState['preferences']['theme']) => void;
  setLanguage: (language: string) => void;
  setTimezone: (timezone: string) => void;
  setAutoSave: (autoSave: boolean) => void;
  setTelemetryEnabled: (enabled: boolean) => void;
  
  // Utility actions
  resetState: () => void;
  importState: (state: Partial<AppState>) => void;
  exportState: () => AppState;
}

// Initial state
const initialState: AppState = {
  user: null,
  isAuthenticated: false,
  
  ui: {
    sidebarOpen: true,
    activePanel: 'stream',
    showExamples: false,
    showHelp: false,
    showOnboarding: false,
    mobileMenuOpen: false,
    mobilePanelOpen: false,
    commandPaletteOpen: false,
    currentTransformationId: null,
  },
  
  editorSettings: {
    fontSize: 14,
    tabSize: 2,
    wordWrap: true,
    minimap: false,
    theme: 'vs-dark',
    autoSave: true,
    formatOnSave: true,
  },
  
  transformation: {
    isRunning: false,
    currentPrompt: '',
    transformedCode: '',
    diffContent: '',
    iterations: 0,
    maxIterations: 10,
    currentScore: 0,
    targetScore: 0.8,
    cost: 0,
    startTime: null,
    endTime: null,
  },
  
  notifications: {
    notifications: [],
    unreadCount: 0,
  },
  
  github: {
    connected: false,
    user: null,
    repositories: [],
    selectedRepo: null,
  },
  
  preferences: {
    theme: 'dark',
    language: 'en',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    autoSave: true,
    telemetryEnabled: true,
  },
};

// Create the store with middleware
export const useAppStore = create<AppState & AppActions>()(
  subscribeWithSelector(
    persist(
      immer((set, get) => ({
        ...initialState,
        
        // User actions
        setUser: (user) => set((state) => {
          state.user = user;
          state.isAuthenticated = !!user;
        }),
        
        setAuthenticated: (authenticated) => set((state) => {
          state.isAuthenticated = authenticated;
        }),
        
        logout: () => set((state) => {
          state.user = null;
          state.isAuthenticated = false;
          state.github.connected = false;
          state.github.user = null;
          state.github.repositories = [];
          state.github.selectedRepo = null;
        }),
        
        // UI actions
        toggleSidebar: () => set((state) => {
          state.ui.sidebarOpen = !state.ui.sidebarOpen;
        }),
        
        setSidebarOpen: (open) => set((state) => {
          state.ui.sidebarOpen = open;
        }),
        
        setActivePanel: (panel) => set((state) => {
          state.ui.activePanel = panel;
        }),
        
        toggleExamples: () => set((state) => {
          state.ui.showExamples = !state.ui.showExamples;
          if (state.ui.showExamples) {
            state.ui.showHelp = false;
          }
        }),
        
        toggleHelp: () => set((state) => {
          state.ui.showHelp = !state.ui.showHelp;
          if (state.ui.showHelp) {
            state.ui.showExamples = false;
          }
        }),
        
        toggleOnboarding: () => set((state) => {
          state.ui.showOnboarding = !state.ui.showOnboarding;
        }),
        
        setMobileMenuOpen: (open) => set((state) => {
          state.ui.mobileMenuOpen = open;
        }),
        
        setMobilePanelOpen: (open) => set((state) => {
          state.ui.mobilePanelOpen = open;
        }),
        
        toggleCommandPalette: () => set((state) => {
          state.ui.commandPaletteOpen = !state.ui.commandPaletteOpen;
        }),
        
        setCurrentTransformationId: (id) => set((state) => {
          state.ui.currentTransformationId = id;
        }),
        
        // Editor actions
        updateEditorSettings: (settings) => set((state) => {
          Object.assign(state.editorSettings, settings);
        }),
        
        resetEditorSettings: () => set((state) => {
          state.editorSettings = initialState.editorSettings;
        }),
        
        // Transformation actions
        startTransformation: (prompt, options = {}) => set((state) => {
          state.transformation.isRunning = true;
          state.transformation.currentPrompt = prompt;
          state.transformation.startTime = new Date();
          state.transformation.endTime = null;
          state.transformation.iterations = 0;
          state.transformation.currentScore = 0;
          state.transformation.cost = 0;
          Object.assign(state.transformation, options);
        }),
        
        stopTransformation: () => set((state) => {
          state.transformation.isRunning = false;
          state.transformation.endTime = new Date();
        }),
        
        updateTransformationProgress: (updates) => set((state) => {
          Object.assign(state.transformation, updates);
        }),
        
        resetTransformation: () => set((state) => {
          state.transformation = initialState.transformation;
        }),
        
        // Notification actions
        addNotification: (notification) => set((state) => {
          const newNotification = {
            ...notification,
            id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date(),
            read: false,
          };
          state.notifications.notifications.unshift(newNotification);
          state.notifications.unreadCount += 1;
          
          // Keep only last 100 notifications
          if (state.notifications.notifications.length > 100) {
            state.notifications.notifications = state.notifications.notifications.slice(0, 100);
          }
        }),
        
        markNotificationRead: (id) => set((state) => {
          const notification = state.notifications.notifications.find(n => n.id === id);
          if (notification && !notification.read) {
            notification.read = true;
            state.notifications.unreadCount = Math.max(0, state.notifications.unreadCount - 1);
          }
        }),
        
        markAllNotificationsRead: () => set((state) => {
          state.notifications.notifications.forEach(n => n.read = true);
          state.notifications.unreadCount = 0;
        }),
        
        removeNotification: (id) => set((state) => {
          const index = state.notifications.notifications.findIndex(n => n.id === id);
          if (index !== -1) {
            const notification = state.notifications.notifications[index];
            if (!notification.read) {
              state.notifications.unreadCount = Math.max(0, state.notifications.unreadCount - 1);
            }
            state.notifications.notifications.splice(index, 1);
          }
        }),
        
        clearNotifications: () => set((state) => {
          state.notifications.notifications = [];
          state.notifications.unreadCount = 0;
        }),
        
        // GitHub actions
        setGitHubConnected: (connected) => set((state) => {
          state.github.connected = connected;
        }),
        
        setGitHubUser: (user) => set((state) => {
          state.github.user = user;
        }),
        
        setGitHubRepositories: (repos) => set((state) => {
          state.github.repositories = repos;
        }),
        
        setSelectedRepository: (repo) => set((state) => {
          state.github.selectedRepo = repo;
        }),
        
        // Preference actions
        setTheme: (theme) => set((state) => {
          state.preferences.theme = theme;
        }),
        
        setLanguage: (language) => set((state) => {
          state.preferences.language = language;
        }),
        
        setTimezone: (timezone) => set((state) => {
          state.preferences.timezone = timezone;
        }),
        
        setAutoSave: (autoSave) => set((state) => {
          state.preferences.autoSave = autoSave;
        }),
        
        setTelemetryEnabled: (enabled) => set((state) => {
          state.preferences.telemetryEnabled = enabled;
        }),
        
        // Utility actions
        resetState: () => set(() => initialState),
        
        importState: (newState) => set((state) => {
          Object.assign(state, newState);
        }),
        
        exportState: () => get(),
      })),
      {
        name: 'metamorphic-reactor-store',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          // Only persist certain parts of the state
          editorSettings: state.editorSettings,
          preferences: state.preferences,
          ui: {
            sidebarOpen: state.ui.sidebarOpen,
            activePanel: state.ui.activePanel,
          },
          github: {
            connected: state.github.connected,
            selectedRepo: state.github.selectedRepo,
          },
        }),
      }
    )
  )
);
