import { useEffect, useRef, useCallback } from 'react';

// Focus management utilities
export interface FocusableElement extends HTMLElement {
  focus(): void;
  blur(): void;
  tabIndex: number;
}

// Get all focusable elements within a container
export const getFocusableElements = (container: HTMLElement): FocusableElement[] => {
  const focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]',
    'audio[controls]',
    'video[controls]',
    'iframe',
    'object',
    'embed',
    'area[href]',
    'summary',
  ].join(', ');

  const elements = Array.from(container.querySelectorAll(focusableSelectors)) as FocusableElement[];
  
  return elements.filter(element => {
    // Check if element is visible and not disabled
    const style = window.getComputedStyle(element);
    return (
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0' &&
      element.offsetWidth > 0 &&
      element.offsetHeight > 0 &&
      !element.hasAttribute('disabled') &&
      element.tabIndex !== -1
    );
  });
};

// Focus trap hook for modals and dialogs
export const useFocusTrap = (isActive: boolean = true) => {
  const containerRef = useRef<HTMLElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = getFocusableElements(container);
    
    if (focusableElements.length === 0) return;

    // Store the previously focused element
    previousFocusRef.current = document.activeElement as HTMLElement;

    // Focus the first focusable element
    focusableElements[0].focus();

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];
      const activeElement = document.activeElement as HTMLElement;

      if (event.shiftKey) {
        // Shift + Tab (backward)
        if (activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab (forward)
        if (activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // Allow parent components to handle escape
        event.stopPropagation();
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    container.addEventListener('keydown', handleEscape);

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
      container.removeEventListener('keydown', handleEscape);
      
      // Restore focus to the previously focused element
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    };
  }, [isActive]);

  return containerRef;
};

// Roving tabindex hook for lists and grids
export const useRovingTabIndex = (orientation: 'horizontal' | 'vertical' | 'both' = 'vertical') => {
  const containerRef = useRef<HTMLElement>(null);
  const currentIndexRef = useRef(0);

  const updateTabIndexes = useCallback((activeIndex: number) => {
    if (!containerRef.current) return;

    const focusableElements = getFocusableElements(containerRef.current);
    
    focusableElements.forEach((element, index) => {
      element.tabIndex = index === activeIndex ? 0 : -1;
    });

    currentIndexRef.current = activeIndex;
  }, []);

  const focusItem = useCallback((index: number) => {
    if (!containerRef.current) return;

    const focusableElements = getFocusableElements(containerRef.current);
    
    if (index >= 0 && index < focusableElements.length) {
      updateTabIndexes(index);
      focusableElements[index].focus();
    }
  }, [updateTabIndexes]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!containerRef.current) return;

    const focusableElements = getFocusableElements(containerRef.current);
    const currentIndex = currentIndexRef.current;

    let newIndex = currentIndex;

    switch (event.key) {
      case 'ArrowDown':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          newIndex = (currentIndex + 1) % focusableElements.length;
        }
        break;
      case 'ArrowUp':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          newIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1;
        }
        break;
      case 'ArrowRight':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          newIndex = (currentIndex + 1) % focusableElements.length;
        }
        break;
      case 'ArrowLeft':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          newIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1;
        }
        break;
      case 'Home':
        event.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        event.preventDefault();
        newIndex = focusableElements.length - 1;
        break;
    }

    if (newIndex !== currentIndex) {
      focusItem(newIndex);
    }
  }, [orientation, focusItem]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Initialize with first item focused
    updateTabIndexes(0);

    container.addEventListener('keydown', handleKeyDown);

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, updateTabIndexes]);

  return {
    containerRef,
    focusItem,
    currentIndex: currentIndexRef.current,
  };
};

// Skip links hook for accessibility
export const useSkipLinks = () => {
  const skipLinksRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Show skip links on first Tab press
      if (event.key === 'Tab' && !event.shiftKey) {
        const skipLinks = skipLinksRef.current;
        if (skipLinks && skipLinks.style.transform === 'translateY(-100%)') {
          skipLinks.style.transform = 'translateY(0)';
        }
      }
    };

    const handleFocusOut = (event: FocusEvent) => {
      const skipLinks = skipLinksRef.current;
      if (skipLinks && !skipLinks.contains(event.relatedTarget as Node)) {
        skipLinks.style.transform = 'translateY(-100%)';
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('focusout', handleFocusOut);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('focusout', handleFocusOut);
    };
  }, []);

  return skipLinksRef;
};

// Focus restoration hook
export const useFocusRestore = () => {
  const previousFocusRef = useRef<HTMLElement | null>(null);

  const saveFocus = useCallback(() => {
    previousFocusRef.current = document.activeElement as HTMLElement;
  }, []);

  const restoreFocus = useCallback(() => {
    if (previousFocusRef.current) {
      previousFocusRef.current.focus();
      previousFocusRef.current = null;
    }
  }, []);

  return { saveFocus, restoreFocus };
};

// Keyboard navigation hook for complex components
export const useKeyboardNavigation = (options: {
  onEscape?: () => void;
  onEnter?: () => void;
  onSpace?: () => void;
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onHome?: () => void;
  onEnd?: () => void;
  onPageUp?: () => void;
  onPageDown?: () => void;
  preventDefault?: string[];
} = {}) => {
  const {
    onEscape,
    onEnter,
    onSpace,
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onHome,
    onEnd,
    onPageUp,
    onPageDown,
    preventDefault = [],
  } = options;

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const { key } = event;

    if (preventDefault.includes(key)) {
      event.preventDefault();
    }

    switch (key) {
      case 'Escape':
        onEscape?.();
        break;
      case 'Enter':
        onEnter?.();
        break;
      case ' ':
        onSpace?.();
        break;
      case 'ArrowUp':
        onArrowUp?.();
        break;
      case 'ArrowDown':
        onArrowDown?.();
        break;
      case 'ArrowLeft':
        onArrowLeft?.();
        break;
      case 'ArrowRight':
        onArrowRight?.();
        break;
      case 'Home':
        onHome?.();
        break;
      case 'End':
        onEnd?.();
        break;
      case 'PageUp':
        onPageUp?.();
        break;
      case 'PageDown':
        onPageDown?.();
        break;
    }
  }, [
    onEscape,
    onEnter,
    onSpace,
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onHome,
    onEnd,
    onPageUp,
    onPageDown,
    preventDefault,
  ]);

  return { handleKeyDown };
};

// Focus visible hook for custom focus indicators
export const useFocusVisible = () => {
  const [isFocusVisible, setIsFocusVisible] = React.useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    let hadKeyboardEvent = false;

    const handleKeyDown = () => {
      hadKeyboardEvent = true;
    };

    const handleMouseDown = () => {
      hadKeyboardEvent = false;
    };

    const handleFocus = () => {
      setIsFocusVisible(hadKeyboardEvent);
    };

    const handleBlur = () => {
      setIsFocusVisible(false);
    };

    document.addEventListener('keydown', handleKeyDown, true);
    document.addEventListener('mousedown', handleMouseDown, true);
    element.addEventListener('focus', handleFocus);
    element.addEventListener('blur', handleBlur);

    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
      document.removeEventListener('mousedown', handleMouseDown, true);
      element.removeEventListener('focus', handleFocus);
      element.removeEventListener('blur', handleBlur);
    };
  }, []);

  return { isFocusVisible, elementRef };
};

export default {
  useFocusTrap,
  useRovingTabIndex,
  useSkipLinks,
  useFocusRestore,
  useKeyboardNavigation,
  useFocusVisible,
  getFocusableElements,
};
