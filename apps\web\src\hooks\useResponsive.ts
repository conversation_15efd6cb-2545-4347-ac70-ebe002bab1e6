import { useState, useEffect, useCallback } from 'react';

// Breakpoint definitions (matching Tailwind CSS defaults)
export const BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof BREAKPOINTS;

// Device type detection
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// Orientation type
export type Orientation = 'portrait' | 'landscape';

// Screen info interface
export interface ScreenInfo {
  width: number;
  height: number;
  breakpoint: Breakpoint;
  deviceType: DeviceType;
  orientation: Orientation;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouch: boolean;
  pixelRatio: number;
}

// Get current breakpoint based on width
const getBreakpoint = (width: number): Breakpoint => {
  if (width >= BREAKPOINTS['2xl']) return '2xl';
  if (width >= BREAKPOINTS.xl) return 'xl';
  if (width >= BREAKPOINTS.lg) return 'lg';
  if (width >= BREAKPOINTS.md) return 'md';
  if (width >= BREAKPOINTS.sm) return 'sm';
  return 'xs';
};

// Get device type based on width and touch capability
const getDeviceType = (width: number, isTouch: boolean): DeviceType => {
  if (width < BREAKPOINTS.md) return 'mobile';
  if (width < BREAKPOINTS.lg && isTouch) return 'tablet';
  return 'desktop';
};

// Get orientation
const getOrientation = (width: number, height: number): Orientation => {
  return width > height ? 'landscape' : 'portrait';
};

// Detect touch capability
const isTouchDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    // @ts-ignore
    navigator.msMaxTouchPoints > 0
  );
};

// Main responsive hook
export const useResponsive = () => {
  const [screenInfo, setScreenInfo] = useState<ScreenInfo>(() => {
    if (typeof window === 'undefined') {
      return {
        width: 1024,
        height: 768,
        breakpoint: 'lg',
        deviceType: 'desktop',
        orientation: 'landscape',
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isTouch: false,
        pixelRatio: 1,
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;
    const isTouch = isTouchDevice();
    const breakpoint = getBreakpoint(width);
    const deviceType = getDeviceType(width, isTouch);
    const orientation = getOrientation(width, height);

    return {
      width,
      height,
      breakpoint,
      deviceType,
      orientation,
      isMobile: deviceType === 'mobile',
      isTablet: deviceType === 'tablet',
      isDesktop: deviceType === 'desktop',
      isTouch,
      pixelRatio: window.devicePixelRatio || 1,
    };
  });

  const updateScreenInfo = useCallback(() => {
    if (typeof window === 'undefined') return;

    const width = window.innerWidth;
    const height = window.innerHeight;
    const isTouch = isTouchDevice();
    const breakpoint = getBreakpoint(width);
    const deviceType = getDeviceType(width, isTouch);
    const orientation = getOrientation(width, height);

    setScreenInfo({
      width,
      height,
      breakpoint,
      deviceType,
      orientation,
      isMobile: deviceType === 'mobile',
      isTablet: deviceType === 'tablet',
      isDesktop: deviceType === 'desktop',
      isTouch,
      pixelRatio: window.devicePixelRatio || 1,
    });
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Debounce resize events
    let timeoutId: NodeJS.Timeout;
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateScreenInfo, 150);
    };

    window.addEventListener('resize', debouncedUpdate);
    window.addEventListener('orientationchange', debouncedUpdate);

    // Initial update
    updateScreenInfo();

    return () => {
      window.removeEventListener('resize', debouncedUpdate);
      window.removeEventListener('orientationchange', debouncedUpdate);
      clearTimeout(timeoutId);
    };
  }, [updateScreenInfo]);

  return screenInfo;
};

// Hook for specific breakpoint matching
export const useBreakpoint = (breakpoint: Breakpoint) => {
  const { width } = useResponsive();
  return width >= BREAKPOINTS[breakpoint];
};

// Hook for breakpoint range matching
export const useBreakpointRange = (min?: Breakpoint, max?: Breakpoint) => {
  const { width } = useResponsive();
  
  const minWidth = min ? BREAKPOINTS[min] : 0;
  const maxWidth = max ? BREAKPOINTS[max] - 1 : Infinity;
  
  return width >= minWidth && width <= maxWidth;
};

// Hook for media query matching
export const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener('change', handler);

    return () => {
      mediaQuery.removeEventListener('change', handler);
    };
  }, [query]);

  return matches;
};

// Hook for container queries (when supported)
export const useContainerQuery = (containerRef: React.RefObject<HTMLElement>, query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (!containerRef.current || typeof window === 'undefined') return;

    // Fallback to ResizeObserver for container-like behavior
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        
        // Simple width-based container query simulation
        if (query.includes('min-width')) {
          const minWidth = parseInt(query.match(/min-width:\s*(\d+)px/)?.[1] || '0');
          setMatches(width >= minWidth);
        } else if (query.includes('max-width')) {
          const maxWidth = parseInt(query.match(/max-width:\s*(\d+)px/)?.[1] || '9999');
          setMatches(width <= maxWidth);
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef, query]);

  return matches;
};

// Responsive value hook - returns different values based on breakpoint
export const useResponsiveValue = <T>(values: Partial<Record<Breakpoint, T>>) => {
  const { breakpoint } = useResponsive();
  
  // Find the best matching value for current breakpoint
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpointOrder.indexOf(breakpoint);
  
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  // Fallback to the first available value
  for (const bp of breakpointOrder) {
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  return undefined;
};

// Viewport utilities
export const useViewportSize = () => {
  const { width, height } = useResponsive();
  return { width, height };
};

export const useOrientation = () => {
  const { orientation } = useResponsive();
  return orientation;
};

export const useDeviceType = () => {
  const { deviceType, isMobile, isTablet, isDesktop } = useResponsive();
  return { deviceType, isMobile, isTablet, isDesktop };
};

export const useIsTouch = () => {
  const { isTouch } = useResponsive();
  return isTouch;
};

// Safe area utilities for mobile devices
export const useSafeArea = () => {
  const [safeArea, setSafeArea] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateSafeArea = () => {
      const style = getComputedStyle(document.documentElement);
      setSafeArea({
        top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
        right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
        bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
        left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
      });
    };

    updateSafeArea();
    window.addEventListener('resize', updateSafeArea);
    window.addEventListener('orientationchange', updateSafeArea);

    return () => {
      window.removeEventListener('resize', updateSafeArea);
      window.removeEventListener('orientationchange', updateSafeArea);
    };
  }, []);

  return safeArea;
};

// Responsive utilities object
export const responsive = {
  useResponsive,
  useBreakpoint,
  useBreakpointRange,
  useMediaQuery,
  useContainerQuery,
  useResponsiveValue,
  useViewportSize,
  useOrientation,
  useDeviceType,
  useIsTouch,
  useSafeArea,
  BREAKPOINTS,
};

export default responsive;
